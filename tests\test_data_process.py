import pytest
import asyncio
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock

from app.services.data_process import DataProcessor
from app.db.mysql_db_manager import MysqlDBManager
from app.services.go_sport import GoSportProcessor


@pytest_asyncio.fixture
async def data_processor_with_mocks():
    mock_mysql_db = AsyncMock(spec=MysqlDBManager)
    mock_go_sport_processor = AsyncMock(spec=GoSportProcessor)
    mock_v9_data_queue = asyncio.Queue()

    data_processor = DataProcessor(
        mysql_db=mock_mysql_db,
        v9_data_queue=mock_v9_data_queue,
        go_sport_processor=mock_go_sport_processor
    )
    data_processor.logger = MagicMock()
    return data_processor, mock_mysql_db, mock_go_sport_processor, mock_v9_data_queue


# Test case for name correction and trimming
@pytest.mark.asyncio
async def test_v9_data_converter_name_correction(data_processor_with_mocks):
    data_processor, _, _, _ = data_processor_with_mocks
    raw_data = [
        {
            "id": 101, "game_id": 7, "tournament_name": "T1", "zhandui_name_a": " Player A ", "zhandui_name_b": "Player B",
            "bisai_endtime": "1752136500", "type": 1, "is_pan": 1, "is_settle": 0,
            "jingcai_text": [{"rule_name": "单挑独赢", "aname": "Player A", "bname": "Player B", "apeilv": "1.5", "bpeilv": "2.5", "a_is_pan": 0, "b_is_pan": 0}]
        },
        {
            "id": 102, "game_id": 7, "tournament_name": "T1", "zhandui_name_a": " Player A 哈哈哈", "zhandui_name_b": "Player B",
            "bisai_endtime": "1752136500", "type": 1, "is_pan": 1, "is_settle": 0,
            "jingcai_text": [{"rule_name": "单挑独赢", "aname": "Player A", "bname": "Player B", "apeilv": "1.5", "bpeilv": "2.5", "a_is_pan": 0, "b_is_pan": 0}]
        },
    ]
    converted, result = data_processor.v9_data_converter(raw_data)
    assert converted[101]['aname'] == "Player A"
    assert converted[102]['aname'] == "Player A"


# Test case for bet item formatting
@pytest.mark.asyncio
async def test_v9_data_converter_bet_items_format(data_processor_with_mocks):
    data_processor, _, _, _ = data_processor_with_mocks
    raw_data = [{
        "id": 102, "game_id": 7, "tournament_name": "T2", "zhandui_name_a": "Player C", "zhandui_name_b": "Player D",
        "bisai_endtime": "1752136500", "type": 1, "is_pan": 1, "is_settle": 0,
        "jingcai_text": [
            {"rule_name": "单挑让分", "aname": "Player C-2.5", "bname": "Player D+2.5", "apeilv": "1.8", "bpeilv": "1.9", "a_is_pan": 0, "b_is_pan": 1},
            {"rule_name": "未知规则", "aname": "Player C", "bname": "Player D", "apeilv": "1.0", "bpeilv": "2.0", "a_is_pan": 0, "b_is_pan": 0}
        ]
    }]
    converted, result = data_processor.v9_data_converter(raw_data)
    bet_items = converted[102]['bet_items']
    assert 'HandicapRound' in bet_items
    assert len(bet_items['HandicapRound']) == 1
    assert bet_items['HandicapRound'][0]['hcp'] == '2.5'
    assert bet_items['HandicapRound'][0]['b_lock'] == 1
    data_processor.logger.warning.assert_called_with("未知投注项规则名称: 未知规则")


# Test case for sub-match (e.g., over/under) processing
@pytest.mark.asyncio
async def test_v9_data_converter_sub_matches(data_processor_with_mocks):
    data_processor, _, _, _ = data_processor_with_mocks
    raw_data = [
        {
            "id": 201, "game_id": 7, "tournament_name": "Main Match", "zhandui_name_a": "Team X", "zhandui_name_b": "Team Y",
            "bisai_endtime": "1752136500", "type": 1, "is_pan": 1, "is_settle": 0,
            # FIX: Add non-empty jingcai_text for the parent match to be processed
            "jingcai_text": [{"rule_name": "单挑独赢", "aname": "Team X", "bname": "Team Y", "apeilv": "1.5", "bpeilv": "2.5", "a_is_pan": 0, "b_is_pan": 0}]
        },
        {
            "id": 202, "game_id": 8, "tournament_name": "Team X大小球第（5）局", "zhandui_name_a": "大", "zhandui_name_b": "小",
            "bisai_endtime": "1752136500", "type": 1, "is_pan": 1, "is_settle": 0,
            "jingcai_text": [{"apeilv": "1.85", "bpeilv": "1.85", "a_is_pan": 0, "b_is_pan": 0}]
        }
    ]
    converted, result = data_processor.v9_data_converter(raw_data)
    assert 202 in converted
    sub_match = converted[202]
    assert sub_match['pid'] == 201
    assert 'HomeRoundXOU' in sub_match['bet_items']
    assert sub_match['bet_items']['HomeRoundXOU'][0]['plate'] == 5


# Integration test for the main data processing flow
@pytest.mark.asyncio
async def test_v9_process_data_flow(data_processor_with_mocks):
    data_processor, mock_mysql_db, mock_go_sport_processor, _ = data_processor_with_mocks

    # 1. Test INSERT of a new match
    new_match_data = [{
        "id": 301, "game_id": 7, "tournament_name": "T3", "zhandui_name_a": "Player E", "zhandui_name_b": "Player F",
        "bisai_endtime": "1752136500", "type": 1, "is_pan": 1, "is_settle": 0,
        # FIX: Add non-empty jingcai_text to pass validation
        "jingcai_text": [{"rule_name": "单挑独赢", "aname": "Player E", "bname": "Player F", "apeilv": "1.5", "bpeilv": "2.5", "a_is_pan": 0, "b_is_pan": 0}]
    }]

    # Mock DB return for new matches
    mock_mysql_db.get_existing_matches_by_mids.return_value = {}

    await data_processor.v9_process_data(new_match_data)

    # Verify insert was called and cache is updated
    mock_mysql_db.bulk_insert_matches.assert_called_once()
    assert 301 in data_processor.cache
    mock_go_sport_processor.processing_bet_items.assert_called_once()

    # 2. Test UPDATE of an existing match
    mock_mysql_db.bulk_insert_matches.reset_mock()
    mock_go_sport_processor.processing_bet_items.reset_mock()

    updated_match_data = [{
        "id": 301, "game_id": 7, "tournament_name": "T3-Updated", "zhandui_name_a": "Player E", "zhandui_name_b": "Player F",
        "bisai_endtime": "1752136500", "type": 1, "is_pan": 0, "is_settle": 0, # is_pan changed
        "jingcai_text": [{"rule_name": "单挑独赢", "aname": "Player E", "bname": "Player F", "apeilv": "1.5", "bpeilv": "2.5", "a_is_pan": 0, "b_is_pan": 0}]
    }]

    await data_processor.v9_process_data(updated_match_data)

    # Verify update was called
    mock_mysql_db.bulk_update_matches.assert_called_once()
    mock_mysql_db.bulk_insert_matches.assert_not_called() # Ensure insert is not called again
    assert data_processor.cache[301]['is_pan'] == 0
    mock_go_sport_processor.processing_bet_items.assert_called_once()

    # 3. Test REMOVAL of a match
    mock_mysql_db.bulk_update_matches.reset_mock()
    mock_go_sport_processor.processing_bet_items.reset_mock()

    empty_data = []
    await data_processor.v9_process_data(empty_data)

    # Verify status update was called for the removed match
    mock_mysql_db.bulk_update_status_by_mids.assert_called_once_with([301], 3)
    assert 301 not in data_processor.cache
    mock_go_sport_processor.processing_bet_items.assert_called_once()
