CREATE TABLE matches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    game_id INT NULL,
    mid INT NOT NULL,
    go_mid INT NULL,
    lname TEXT NOT NULL,
    aname TEXT NOT NULL,
    bname TEXT NOT NULL,
    mtime DATETIME NOT NULL,
    `type` TINYINT NOT NULL,
    is_pan TINYINT NOT NULL,
    is_settle TINYINT NOT NULL,
    pid INT NULL,
    bet_items JSON NULL,
    result VARCHAR(100) NULL,
    go_result TINYINT NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT NOW(),
    updated_at DATETIME NOT NULL DEFAULT NOW(),
    UNIQUE KEY uk_mid (mid),
    UNIQUE KEY uk_go_mid (go_mid),
    INDEX (type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE bet_items_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    game_id INT NULL,
    mid INT NOT NULL,
    go_mid INT NULL,
    lname TEXT NOT NULL,
    aname TEXT NOT NULL,
    bname TEXT NOT NULL,
    mtime DATETIME NOT NULL,
    `type` TINYINT NOT NULL,
    is_pan TINYINT NOT NULL,
    is_settle TINYINT NOT NULL,
    pid INT NULL,
    bet_items JSON NULL,
    created_at DATETIME NOT NULL DEFAULT NOW()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;