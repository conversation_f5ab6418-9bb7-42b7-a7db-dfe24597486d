import asyncio
import logging
from datetime import datetime
import re
import json
from app.services.go_sport import GoSportProcessor
from app.db.mysql_db_manager import MysqlDBManager
import copy


class DataProcessor:
    def __init__(self, mysql_db: MysqlDBManager, v9_data_queue, go_sport_processor: GoSportProcessor): 
        self.logger = logging.getLogger(self.__class__.__name__)
        self.v9_data_queue = v9_data_queue
        self.mysql_db: MysqlDBManager = mysql_db
        self.go_sport_processor: GoSportProcessor = go_sport_processor
        self.new_matches_queue = asyncio.Queue(maxsize=100)  # 专门用于处理 Go Sport 数据
        self.cache = {}
        self.go_mid_map = {}
        self.cache_result_id = set()

        """
        申程大小号球第（22）局
        牛壮大小球第（5）局
        郑肖淮双打中8大小号球（16）局
        王大双-赵若同大小球（1）局
        """
        self.re_hcp_4_round_x_ou = re.compile(r'大小(?:球|号球)?[^（(]*[（(](\d+)[）)]局')
        self.re_hcp_4_handicap = re.compile(r'([-+][0-9]+(?:\.[0-9]+)?)')

    def _extract_pure_name(self, name_with_hcp):
        """
        从包含让分信息的队名中提取纯净队名。
        例如："陈思明-3.5" -> "陈思明"
        """
        match = self.re_hcp_4_handicap.search(name_with_hcp)
        if match:
            return name_with_hcp[:match.start()].strip()
        return name_with_hcp.strip()

    async def v9_process_data(self, raw_data):
        """
        处理原始数据。
        """
        try:
            
            # 实际的数据清洗、转换、验证逻辑
            matches, result = self.v9_data_converter(raw_data)
            if result:
                new_result_id = {res.get('mid') for res in result}
                update_id = new_result_id - self.cache_result_id
                if update_id:
                    self.cache_result_id = new_result_id
                    update_result = [res for res in result if res.get('mid') in update_id]
                    await self.mysql_db.bulk_update_results(update_result)

            # 找出新出现的比赛ID
            new_mids = set(matches.keys()) - set(self.cache.keys())
            if new_mids:
                new_matches = await self.mysql_db.get_existing_matches_by_mids(new_mids)
                if new_matches:
                    self.cache.update(new_matches)
                    for k,v in new_matches.items():
                        if v.get('go_mid'):
                            self.go_mid_map[k] = v.get('go_mid')
                            matches[k]['go_mid'] = v.get('go_mid')



            # 将变化的数据持久化到数据库
            matches_to_insert = []
            matches_to_update = []

            for match_id, match_data in matches.items():
                
                # 检查并转换mtime为字符串格式
                if isinstance(match_data.get('mtime'), datetime):
                    match_data['mtime'] = match_data['mtime'].strftime("%Y-%m-%d %H:%M:%S")


                # pid 可以为 None
                if 'pid' not in match_data:
                    match_data['pid'] = None

                cached_match = self.cache.get(match_id)
                
                # 定义需要比较的字段
                fields_to_compare = ['game_id','lname', 'aname', 'bname', 'mtime', 'type', 'is_pan', 'is_settle', 'pid', 'bet_items']
                
                if not cached_match:
                    # If no cached match, it's a new match to insert
                    matches_to_insert.append(match_data)
                else:
                    has_changed = False
                    diff_data = {'mid': match_id} # Always include mid for identification
                    for field in fields_to_compare:
                        new_value = match_data.get(field)
                        old_value = cached_match.get(field)

                        # 特殊处理 datetime 对象，确保格式一致
                        if field == 'mtime':
                            if isinstance(new_value, datetime):
                                new_value = new_value.strftime("%Y-%m-%d %H:%M:%S")
                            if isinstance(old_value, datetime):
                                old_value = old_value.strftime("%Y-%m-%d %H:%M:%S")
                        
                        # 特殊处理 JSON 字段，确保排序一致
                        if field == 'bet_items':
                            if isinstance(new_value, dict):
                                new_value = json.dumps(new_value, sort_keys=True, ensure_ascii=False)
                            if isinstance(old_value, dict):
                                old_value = json.dumps(old_value, sort_keys=True, ensure_ascii=False)

                        if new_value != old_value:
                            has_changed = True
                            diff_data[field] = match_data.get(field) # Only add changed fields
                    
                    if has_changed:
                        # 如果 lname 或者 aname 或者 bname 或者 mtime 存在diff_data
                        if diff_data.keys() & {'lname', 'aname', 'bname'}:
                            diff_data["go_mid"] = None
                            self.go_mid_map.pop(match_data['mid'], None)

                        # Store both the full new data and the diff data
                        matches_to_update.append({'full_data': match_data, 'diff_data': diff_data})
                 
            
            # 找出在cache中存在但新数据中不存在的id
            removed_ids = [match_id for match_id in self.cache if match_id not in matches]
            
            
            if matches_to_insert:
                await self.mysql_db.bulk_insert_matches(matches_to_insert)
                await self.new_matches_queue.put(1)
            
            if matches_to_update:
                await self.mysql_db.bulk_update_matches(matches_to_update)
            
            # 批量更新被移除比赛的状态
            if removed_ids:
                await self.mysql_db.bulk_update_status_by_mids(removed_ids, 3) # type 3表示已结束/无效
            
            # 更新cache
            self.cache = copy.deepcopy(matches)
            self.go_mid_map = {v['mid']: v['go_mid'] for k, v in matches.items() if v.get('go_mid') is not None}
            # 只处理主赛事和type=1的赛事
            filter_matches = [m for m in matches.values() if m['type'] == 1 and m['pid'] == 0]
            await self.go_sport_processor.processing_bet_items(filter_matches)
            return True
        except Exception as e:
            self.logger.exception(f"处理数据时发生错误: {e}")
            return False

    # 中文名字
    NAME_REG = re.compile( r"^[\u4e00-\u9fa5\u00B7\u2022\u2024\u2027\u30FB\uFF65\.]+$" )

    def v9_data_converter(self, raw_data):
        """
        将V9接口的原始数据转换为统一格式。
        """
        try:
            
            main_matches = [match for match in raw_data if match["game_id"] in [1, 7]]
            sub_matches = [match for match in raw_data if match["game_id"] == 8]

            formated_main_matches = {}
            formated_sub_matches = {}
            pid_map = {}
            result = []
            for data in main_matches:
                data["zhandui_name_a"] = data["zhandui_name_a"].strip()
                data["zhandui_name_b"] = data["zhandui_name_b"].strip()

                original_aname = data["zhandui_name_a"]
                original_bname = data["zhandui_name_b"]
                
                # 标记是否需要忽略此赛事
                ignore_match = False

                # 检查并修正 aname
                found_aname_in_jingcai = False
                for item in data.get("jingcai_text", []):
                    pure_aname_in_item = self._extract_pure_name(item.get("aname", ""))
                    if pure_aname_in_item:
                        found_aname_in_jingcai = True
                        if pure_aname_in_item == original_aname: # 如果已经一致，则无需继续
                            break
                        if pure_aname_in_item != original_aname and pure_aname_in_item in original_aname:
                            # self.logger.info(f"修正主队名: 比赛ID {data['id']}, 从 '{original_aname}' 修正为 '{pure_aname_in_item}'")
                            data["zhandui_name_a"] = pure_aname_in_item
                        break 

                if not found_aname_in_jingcai or data["zhandui_name_a"] != original_aname and data["zhandui_name_a"] not in original_aname:
                    # 如果 jingcai_text 中没有找到 aname 或者修正后仍然不匹配且不是子串，则标记为忽略
                    self.logger.warning(f"赛事ID {data['id']} 的主队名 '{original_aname}' 无法与玩法名称匹配或修正，将忽略此赛事。")
                    ignore_match = True
                
                if ignore_match:
                    continue # 忽略此赛事

                # 检查并修正 bname
                found_bname_in_jingcai = False
                for item in data.get("jingcai_text", []):
                    pure_bname_in_item = self._extract_pure_name(item.get("bname", ""))
                    if pure_bname_in_item:
                        found_bname_in_jingcai = True
                        if pure_bname_in_item == original_bname: # 如果已经一致，则无需继续
                            break
                        if pure_bname_in_item != original_bname and pure_bname_in_item in original_bname:
                            # self.logger.info(f"修正客队名: 比赛ID {data['id']}, 从 '{original_bname}' 修正为 '{pure_bname_in_item}'")
                            data["zhandui_name_b"] = pure_bname_in_item
                        break 
                
                if not found_bname_in_jingcai or data["zhandui_name_b"] != original_bname and data["zhandui_name_b"] not in original_bname:
                    # 如果 jingcai_text 中没有找到 bname 或者修正后仍然不匹配且不是子串，则标记为忽略
                    self.logger.warning(f"赛事ID {data['id']} 的客队名 '{original_bname}' 无法与玩法名称匹配或修正，将忽略此赛事。")
                    ignore_match = True

                if ignore_match:
                    continue # 忽略此赛事

                # 如果已经结束
                if data["is_settle"] == 1:
                    a_score = data.get("a_jushu") if data.get("a_jushu")  != "" else 0
                    b_score = data.get("b_jushu") if data.get("b_jushu")  != "" else 0
                    result.append({
                        "mid": data["id"],
                        "result": f"{a_score}:{b_score}"
                    })
                    continue

                formated_main_matches[data["id"]] = {
                    "mid": data["id"],
                    "game_id": data["game_id"],
                    "lname": data["tournament_name"],
                    "aname": data["zhandui_name_a"],
                    "bname": data["zhandui_name_b"],
                    "mtime": datetime.fromtimestamp(int(data["bisai_endtime"])).strftime("%Y-%m-%d %H:%M:%S"),
                    "type": data["type"],
                    "is_pan": data["is_pan"],
                    "is_settle": data["is_settle"],
                    "pid": 0,
                    "bet_items": self.format_bet_items(data["jingcai_text"], data["game_id"]) if data.get("jingcai_text") else {},
                    "go_mid": self.go_mid_map.get(data["id"], None)
                }
                
                pid_map[data["zhandui_name_a"]] = (data["id"], "HomeRoundXOU")
                pid_map[data["zhandui_name_b"]] = (data["id"], "GuestRoundXOU")

            for data in sub_matches:
                pid_key = [k for k in pid_map if k in data["tournament_name"]]
                if len(pid_key) == 1:
                    pid, bet_item_name = pid_map[pid_key[0]]
                    hcp = self.get_hcp_4_round_x_ou(data["tournament_name"])
                    if hcp > 0:
                        # 如果已经结束
                        if data["is_settle"] == 1:
                            a_score = data.get("a_jushu") if data.get("a_jushu")  != "" else 0
                            b_score = data.get("b_jushu") if data.get("b_jushu")  != "" else 0
                            result.append({
                                "mid": data["id"],
                                "result": f"{a_score}:{b_score}"
                            })
                            continue
                        bet_items = data.get("jingcai_text", [])
                        formatted_items = {}
                        if len(bet_items) == 1:
                            formatted_items[bet_item_name] = [{
                                "plate": hcp,
                                "hcp": str(hcp),
                                "a": round(float(bet_items[0]["apeilv"]),2),
                                "b": round(float(bet_items[0]["bpeilv"]),2),
                                "a_lock": bet_items[0]["a_is_pan"],
                                "b_lock": bet_items[0]["b_is_pan"]
                            }]
                        formated_sub_matches[data["id"]] = {
                            "mid": data["id"],
                            "game_id": data["game_id"],
                            "lname": data["tournament_name"],
                            "aname": data["zhandui_name_a"],
                            "bname": data["zhandui_name_b"],
                            "type": data["type"],
                            "is_pan": data["is_pan"],
                            "is_settle": data["is_settle"],
                            "mtime": datetime.fromtimestamp(int(data["bisai_endtime"])).strftime("%Y-%m-%d %H:%M:%S"),
                            "pid": pid,
                            "bet_items": formatted_items,
                            "go_mid": None  # FIX: Ensure go_mid key exists for all matches
                        }

            return {**formated_main_matches, **formated_sub_matches}, result
        except Exception as e:
            self.logger.exception(f"转换数据时发生错误: {e}")
            return {}, []
    
    BET_ITEM_MAPPING = {
        "单挑让局": "Handicap{}",
        "单挑让分": "Handicap{}",
        "全场让分": "Handicap{}",
        "单挑独赢": "FullWinner",
        "总局数单双": "Total{}OE",
        "总分单双": "Total{}OE"
    }

    BET_ITEM_GAMEID_MAPPING = {
        1: "Point",
        7: "Round"
    }

    def format_bet_items(self, bet_items, game_id):
        """
        格式化投注项数据。
        """
        formatted_items = {}
        for item in bet_items:

            bet_item_name = self.BET_ITEM_MAPPING.get(item["rule_name"].strip(), None)
            if not bet_item_name:
                self.logger.warning(f"未知投注项规则名称: {item['rule_name']}")
                continue

            bet_item_name = bet_item_name.format(self.BET_ITEM_GAMEID_MAPPING.get(game_id, ""))

            if not formatted_items.get(bet_item_name):
                formatted_items[bet_item_name] = []

            formatted_items[bet_item_name].append({
                "hcp": self.get_hcp_4_handicap(item["aname"]) if bet_item_name in ["HandicapRound", "HandicapPoint"] else "",
                "a": round(float(item["apeilv"]),2),
                "b": round(float(item["bpeilv"]),2),
                "a_lock": item["a_is_pan"],
                "b_lock": item["b_is_pan"],
                "hot": 1
            })

        return formatted_items
    
    
    def get_hcp_4_round_x_ou(self, hcp_str):
        """
        从字符串中提取第几局。例："牛壮大小球第（5）局"
        """
        try:
            match = self.re_hcp_4_round_x_ou.search(hcp_str)
            if match:
                return int(match.group(1))
            else:
                return 0
        except Exception as e:
            self.logger.error(f"提取局数时发生错误: {e}; 输入值: {hcp_str}")
            return 0

    def get_hcp_4_handicap(self, hcp_str):
        """
        从字符串中提取让局数值。例："刘俊岩-2.5","刘俊岩+2.5"
        """
        try:
            match = self.re_hcp_4_handicap.search(hcp_str)
            if match:
                hcp_value = match.group(1)
                if hcp_value.startswith('-'):
                    return hcp_value[1:]
                elif hcp_value.startswith('+'):
                    return '*' + hcp_value[1:]
                return hcp_value
            else:
                return "0"
        except Exception as e:
            self.logger.error(f"提取让局数值时发生错误: {e}; 输入值: {hcp_str}")
            return "0"

    async def start_processing(self):
        """
        启动数据处理主循环。
        """
        self.logger.info("数据处理任务启动。")
        try:
            # 加载redis玩法数据
            await self.go_sport_processor.load_all_hash()
            await self.new_matches_queue.put(1)
            # 首先从数据库加载未完成的比赛到缓存
            unfinished_matches = await self.mysql_db.get_unfinished_matches()
            if unfinished_matches:
                self.cache = {match['mid']: match for match in unfinished_matches}
                self.go_mid_map = {match['mid']: match['go_mid'] for match in unfinished_matches if match.get('go_mid') is not None}

            while True:
                try:
                    data = await self.v9_data_queue.get()
                except asyncio.CancelledError:
                    self.logger.info("数据处理任务在等待队列时被取消。")
                    break

                try:
                    if data is None:
                        continue
                    await self.v9_process_data(data) 
                except asyncio.CancelledError:
                    self.logger.info("数据处理任务在处理数据时被取消。")
                    self.v9_data_queue.task_done()
                    break
                except Exception as e:
                    self.logger.error(f"处理数据时发生错误: {e}")

                self.v9_data_queue.task_done() 

        except asyncio.CancelledError:
            self.logger.info("数据处理任务已停止。")

    def get_mt(self, match_type):
        if match_type == 1:
            return 2
        elif match_type == 2:
            return 3
        return 1

    async def start_go_sport_processing(self):
        """
        处理go_mid的生成
        """
        await asyncio.sleep(10)
        self.logger.info("Go Sport 数据处理任务启动。")
        try:
            # 更新今天内已结束的赛事
            finished_go_mids = await self.mysql_db.get_finished_matches_go_mid()
            if finished_go_mids:
                await self.go_sport_processor.set_match_closed(finished_go_mids)

            while True:
                try:

                    try:
                        await asyncio.wait_for(self.new_matches_queue.get(), timeout=60)
                        self.new_matches_queue.task_done()
                    except asyncio.TimeoutError:
                        pass
                    
                    
                    # 同步赛事
                    go_mid_map = await self.go_sport_processor.sync_matches()
                    if go_mid_map:
                        self.go_mid_map.update(go_mid_map)
                    
                    # 同步go赛果
                    await self.go_sport_processor.sync_result()

                except asyncio.CancelledError:
                    self.logger.info("Go Sport 数据处理任务在等待队列时被取消。")
                    return
                except Exception as e:
                    self.logger.exception(f"处理 Go Sport 数据时发生错误: {e}")

                

        except asyncio.CancelledError:
            self.logger.info("Go Sport 数据处理任务已停止。")
        except Exception as e:
            self.logger.exception(f"启动 Go Sport 数据处理任务时发生错误: {e}")
            return