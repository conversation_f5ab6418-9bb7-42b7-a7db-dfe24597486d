import httpx
import time
import logging
import asyncio

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

last_request_time = None

async def main(): # Changed to async function
    global last_request_time
    current_time = time.time()
    if last_request_time is not None:
        time_diff = current_time - last_request_time
        logging.info(f"Time since last request: {time_diff:.2f} seconds")
    last_request_time = current_time

    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "connection": "keep-alive",
        "content-length": "99",
        "content-type": "application/json;charset=UTF-8",
        "host": "sportapi.fastball2.com",
        "origin": "https://test.f66b88sport.com",
        "referer": "https://test.f66b88sport.com/",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "cross-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    url = "https://sportapi.fastball2.com/v1/match/getList"
    payload = {"languageType":"CMN","current":1,"orderBy":1,"isPC":True,"sportId":1,"type":1,"leagueIds":[22253]}

    # logging.info(f"Sending request to: {url}")
    # logging.info(f"Request payload: {payload}")
    # logging.info(f"Request headers: {headers}")

    try:
        async with httpx.AsyncClient() as client: # Use AsyncClient
            res = await client.post(url, json=payload, headers=headers) # Await the async call
            res.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            logging.info(f"Response status code: {res.status_code}")
            # logging.info(f"Response JSON: {res.json()}")
    except httpx.HTTPError as e:
        logging.error(f"Request failed: {e}")


if __name__ == "__main__":
    async def run_main_loop(): # New async function to run the loop
        for i in range(100):
            await main() # Await main calls
            logging.info(f"========================{i}========================")
            await asyncio.sleep(3) # Await async sleep

    asyncio.run(run_main_loop()) # Run the async loop
