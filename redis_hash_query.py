import asyncio
import json
from app.db.redis_manager import RedisManager
from app.config import get_redis_config

async def main():
    """
    主函数：连接 Redis，获取 hash 1:sport_odds_hot 的所有 key，并筛选 988000662 开头的数据
    """
    # 获取 Redis 配置
    redis_config = get_redis_config()
    
    # 创建 Redis 管理器
    redis_manager = RedisManager(redis_config)
    
    try:
        # 连接到 Redis
        await redis_manager.connect()
        
        if not redis_manager.redis:
            print("无法连接到 Redis 服务器")
            return
        
        # 获取 hash 1:sport_odds_hot 的所有字段和值
        hash_name = "1:sport_odds_hot"
        print(f"正在获取 hash '{hash_name}' 的所有字段和值...")
        
        hash_data = await redis_manager.get_hash(hash_name)
        
        if not hash_data:
            print(f"Hash '{hash_name}' 不存在或为空")
            return
        
        print(f"Hash '{hash_name}' 共有 {len(hash_data)} 个字段")
        
        # 首先找出所有 state = 1 的 key 和对应的 mid
        state_1_keys = []
        state_1_mids = set()  # 使用集合去重
        print("\n正在查找所有 state = 1 的 key...")
        
        for key, value in hash_data.items():
            try:
                # 解析 JSON 数据
                json_data = json.loads(value)
                state_value = json_data.get('state', 'N/A')
                
                # 收集 state = 1 的 key 和 mid
                if state_value == 1:
                    state_1_keys.append(key)
                    mid_value = json_data.get('mid', 'N/A')
                    state_1_mids.add(mid_value)
            except json.JSONDecodeError:
                # 忽略非 JSON 格式的数据
                pass
        
        print(f"找到 {len(state_1_keys)} 个 state = 1 的 key:")
        for key in state_1_keys:
            print(key)
        
        print(f"\nstate = 1 的去重 mid 值 (共 {len(state_1_mids)} 个):")
        for mid in sorted(state_1_mids):
            print(mid)
        
        # 筛选以 988000662 开头的字段
        prefix = "988000662"
        filtered_data = {k: v for k, v in hash_data.items() if k.startswith(prefix)}
        
        print(f"\n以 '{prefix}' 开头的字段共有 {len(filtered_data)} 个:")
        
        # 打印筛选结果，只显示 state 值
        print("\n所有以 '988000662' 开头的字段及其 state 值:")
        for key, value in filtered_data.items():
            try:
                # 解析 JSON 数据
                json_data = json.loads(value)
                state_value = json_data.get('state', 'N/A')
                print(f"字段: {key}, state: {state_value}")
            except json.JSONDecodeError:
                print(f"字段: {key}, state: 解析失败 (非 JSON 格式)")
            
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
    finally:
        # 关闭 Redis 连接
        await redis_manager.close()

if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())