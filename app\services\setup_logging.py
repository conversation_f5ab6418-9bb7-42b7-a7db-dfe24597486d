# 配置日志
import logging
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path
import sys


def setup_logging():
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    log_file_path = log_dir / "crawler.log"

    # 配置根logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 控制台输出
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(console_handler)

    # 文件输出 (按天轮转，UTF-8编码)
    file_handler = TimedRotatingFileHandler(
        log_file_path,
        when="midnight",
        interval=1,
        backupCount=7, # 保留7天的日志
        encoding="utf-8"
    )
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(file_handler)

    # 错误日志文件输出 (按天轮转，UTF-8编码，只记录ERROR及以上级别)
    error_log_file_path = log_dir / "error.log"
    error_file_handler = TimedRotatingFileHandler(
        error_log_file_path,
        when="midnight",
        interval=1,
        backupCount=7, # 保留7天的错误日志
        encoding="utf-8"
    )
    error_file_handler.setLevel(logging.ERROR) # 只处理ERROR及以上级别的日志
    error_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(error_file_handler)

    # 避免httpx日志过多
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('cloudscraper').setLevel(logging.WARNING) # 避免cloudscraper日志过多