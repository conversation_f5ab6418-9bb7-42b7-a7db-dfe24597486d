import logging
from datetime import datetime
from app.db.redis_manager import RedisManager
from app.db.mysql_db_manager import MysqlDBManager
from app.db.mssql_db_manager import MatchDatabaseManager
from app.services.telegram_bot import TelegramBot
import json

# Sport ID for Chinese Billiards
SID_BILLIARDS = 70
HOT_KEY = f"{SID_BILLIARDS}:sport_odds_hot"
OTHER_KEY = f"{SID_BILLIARDS}:sport_odds_other"

class GoSportProcessor:
    """
    Handles the core business logic for processing and storing match data.
    """
    def __init__(self, bet_items_id_mapping, result_id_mapping, match_db: MatchDatabaseManager, redis: RedisManager, mysql_db: MysqlDBManager, tg_bot: TelegramBot):
        """
        Initializes the processor with a database manager instance.
        """
        self.tg_bot = tg_bot
        self.bet_items_id_mapping = bet_items_id_mapping
        self.result_id_mapping = result_id_mapping
        self.match_db: MatchDatabaseManager = match_db
        self.redis: RedisManager = redis
        self.mysql_db: MysqlDBManager = mysql_db
        self.cache_bet_items = {}
        self.cache_go_mid = set()
        self.logger = logging.getLogger(self.__class__.__name__)


    async def get_or_create_mid(self, league_name: str, home_team_name: str, away_team_name: str, match_time: datetime, sid: int, mt: int, state: int, gcount: int) -> int|None:
        """
        Core business logic to process and store match data based on specifications.
        """
        # Use the session factory from the db_manager instance
        async with self.match_db.async_session_factory() as session:
            async with session.begin():  # Start a transaction
                # Call methods from the db_manager instance
                lid = await self.match_db.get_or_create_league(session, league_name, sid)
                home_tid = await self.match_db.get_or_create_team(session, home_team_name, sid)
                away_tid = await self.match_db.get_or_create_team(session, away_team_name, sid)

                go_mid = await self.match_db.get_match_by_lid_aid_bid_mtime(lid, home_tid, away_tid, match_time)
                if go_mid:
                    self.logger.info(f"Match already exists, mid: {go_mid}")
                    return go_mid
                
                if not all([lid, home_tid, away_tid]):
                    self.logger.error("Error: Could not get or create league/team IDs, operation aborted.")
                    return None

                go_mid = await self.match_db.create_match(
                    session,
                    sid=sid,
                    lid=lid,
                    home_tid=home_tid,
                    away_tid=away_tid,
                    match_time=match_time,
                    mt=mt,
                    state=state,
                    gcount=gcount
                )
                
                self.logger.info(f"Business process completed, successfully created match, mid: {go_mid}")
                return go_mid
            

    async def set_match_closed(self, go_mids: list[int]) -> None:
        """
        Set the matches as closed in the database.
        This method updates the 'copen' field to 0 for the given match IDs.
        """
        if not go_mids:
            self.logger.warning("No match IDs provided to set as closed.")
            return
        
        await self.match_db.set_match_closed(set(go_mids))
                
    async def processing_bet_items(self, matches: list[dict]):
        new_bet_items_formated = {}
        main_matches = [m for m in matches if m.get('pid') == 0 and m.get('go_mid') is not None]
        
        new_go_mids = set([m['go_mid'] for m in main_matches])
        merged_go_mids = self.cache_go_mid | new_go_mids
        # 获取赛事信息
        go_match_info = await self.match_db.get_match_info(merged_go_mids) if merged_go_mids else []
        
        cupdate_go_mids = {m['mid'] for m in go_match_info if m['cupdate'] == 1}
        copen_go_mids = {m['mid'] for m in go_match_info if m['copen'] == 1}
        

        closed_mids = self.cache_go_mid - new_go_mids
        if closed_mids & copen_go_mids:
            await self.match_db.set_match_closed(closed_mids & copen_go_mids)
        
        match_update = []
        for match in main_matches:
            if match['go_mid'] not in cupdate_go_mids:
                continue

            bet_items = match.get('bet_items', {})
            if match['is_pan'] == 1:
                bet_items = {key: [{**item, "a_lock": 1, "b_lock": 1} for item in list_vlaue] for key,list_vlaue in bet_items.items() }
            child_matches = [m for m in matches if m.get('pid') == match['mid']]
            if child_matches:
                for child_match in child_matches:
                    c_bet_items = child_match.get('bet_items', {})
                    if c_bet_items:
                        if child_match['is_pan'] == 1:
                            c_bet_items = {key: [{**item, "a_lock": 1, "b_lock": 1} for item in list_value ] for key,list_value in c_bet_items.items() }
                        bet_items.update(c_bet_items)
            if not bet_items:
                self.logger.warning(f"No bet items found for match mid: {match['mid']}")
                continue
            bet_items_formated = await self.generate_bet_items(match['go_mid'], self.get_mt(match['type']), bet_items)
            update_value = {"gcount": len(bet_items_formated), "mid": match['go_mid'], "mt": self.get_mt(match['type']), "mtime": match['mtime']}
            if match["go_mid"] in copen_go_mids: 
                update_value["state"] = 1
            match_update.append(update_value)
            new_bet_items_formated.update(bet_items_formated)
        
        if match_update:
            await self.match_db.batch_update_matches(match_update)
        

        # 对比缓存
        # 获取已关闭的
        closed_bet_items = {k: {**v, "updateAt": datetime.now().strftime('%Y-%m-%d %H:%M:%S'), "state": 2, "odds1": 0, "odds2": 0} 
                             for k, v in self.cache_bet_items.items() if k not in new_bet_items_formated}

        # 推送到redis
        all_bet_items = {**new_bet_items_formated, **closed_bet_items}
        if all_bet_items:
            hot_bet_items = {k: json.dumps(v) for k, v in all_bet_items.items() if v.get('hot', 0) == 1}
            other_bet_items = {k: json.dumps(v) for k, v in all_bet_items.items() if v.get('hot', 0) == 0}
            if hot_bet_items:
                await self.redis.batch_set_hash(HOT_KEY, hot_bet_items)
            if other_bet_items:
                await self.redis.batch_set_hash(OTHER_KEY, other_bet_items)
            # logger.info(f"Bet items updated in self.redis, total items: {len(all_bet_items)}")
        self.cache_go_mid = new_go_mids.copy()
        self.cache_bet_items = new_bet_items_formated.copy()
        

    async def generate_bet_items(self, mid: int, mt: int, bet_items: dict) -> dict:
        bet_items_formated = {}
        for key, item in bet_items.items():
            gid = self.bet_items_id_mapping.get(key, None)
            if gid is None:
                self.logger.warning(f"Bet item ID {key} not found in mapping, skipping.")
                continue
            for i, item2 in enumerate(item):
                plate = item2.get('plate') or i
                id = mid * 1000000 + mt * 100000 + gid * 100 + plate
                bet_items_formated[id] = {
                    "id": id,
                    "mid": mid,
                    "gid": gid,
                    "mt": mt,
                    "sid": SID_BILLIARDS,
                    "smode": 2,
                    "hcp": item2.get('hcp', ''),
                    "plate": plate,
                    "odds1": item2.get('a', 0) if not item2.get('a_lock') else 0,
                    "odds2": item2.get('b', 0) if not item2.get('b_lock') else 0,
                    "odds3": 0,
                    "odds": "",
                    "state": 1,
                    "version": 0,
                    "hot": item2.get('hot', 0),
                    "updateAt": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }
            
        return bet_items_formated
    
    # 缓存所有线上hash
    async def load_all_hash(self):
        hot_hash = await self.redis.get_hash(HOT_KEY)
        other_hash = await self.redis.get_hash(OTHER_KEY)
        hot_hash_json_dict = {k: json.loads(v) for k,v in hot_hash.items()}
        other_hash_json_dict = {k: json.loads(v) for k,v in other_hash.items()}
        open_hot_hash_json_dict = { k: v for k,v in hot_hash_json_dict.items() if v['state'] == 1}
        open_other_hash_json_dict = { k: v for k,v in other_hash_json_dict.items() if v['state'] == 1}
        self.cache_bet_items = {**open_hot_hash_json_dict, **open_other_hash_json_dict}
        self.cache_go_mid = {i['mid'] for i in self.cache_bet_items.values()}
            
    # 更新赛果
    async def update_result(self, mid: int, result: dict, is_main: bool):

        async with self.match_db.async_session_factory() as session:
            async with session.begin():  # Start a transaction
                await self.match_db.upsert_sport_result_crawl(session, result)
                if is_main:
                    await self.match_db.update_match_by_mid(result['mid'], {"rstate":1})
                await self.mysql_db.update_match_by_mid(mid, {"go_result": 1})
        
    # 同步赛果
    async def sync_result(self):
        try:
            # 同步赛果处理
            pending_sync_results = await self.mysql_db.get_un_sync_result_matches()
            go_mid_map = {v['mid']: v['go_mid'] for v in pending_sync_results if v['pid'] == 0 and v["go_mid"] }
            if pending_sync_results:
                for match in pending_sync_results:
                    score = match['result'].split(':')
                    # 子赛事特殊玩法
                    if match['pid'] > 0:
                        go_mid = go_mid_map.get(match['pid'], None)
                        if not go_mid:
                            go_mid = await self.mysql_db.get_go_mid(match['pid'])
                        if go_mid:
                            bet_items = match.get('bet_items', {})
                            result_mapping = self.result_id_mapping.get("game_id_8", {})
                            for k, v in bet_items.items():
                                
                                if k in result_mapping and len(v) == 1 and len(score) == 2:
                                    a = int(score[0])
                                    b = int(score[1])

                                    result = {
                                        "mid": go_mid,
                                        "sid": SID_BILLIARDS,
                                        "hcp": v[0]['hcp'],
                                        "type": 2,
                                        "state": 2 if a == 0 and b == 0 else 1,
                                        "a": 1 if a > b else 2 if a < b else 0,
                                        "b": 0,
                                        "c": "",
                                        **result_mapping[k]
                                    }
                                    await self.update_result(match['mid'], result, False)
                        else:
                            self.logger.error(f"未找到go_mid: {match['mid']}")
                        continue
                    # 主赛事
                    result_mapping = self.result_id_mapping.get(f"game_id_{match['game_id']}", {})
                    
                    if result_mapping and match['go_mid'] and len(score) == 2:
                        a = int(score[0])
                        b = int(score[1])
                        result = {
                            "mid": match['go_mid'],
                            "sid": SID_BILLIARDS,
                            "hcp": 0,
                            "type": 1,
                            "state": 2 if a == 0 and b == 0 else 1,
                            "a": a,
                            "b": b,
                            "c": "",
                            **result_mapping
                        }
                        await self.update_result(match['mid'], result, True)


        except Exception as e:
            self.logger.exception(f"同步赛果时发生错误: {e}")

    # 同步赛事
    async def sync_matches(self) -> dict:
        go_mid_map = {}
        try:
            matches = await self.mysql_db.get_go_mid_null_matches()
            # 创建go赛事
            if matches:
                for match in matches:
                    go_mid = await self.get_or_create_mid(
                        match["lname"], 
                        match["aname"], 
                        match["bname"], 
                        match["mtime"], 
                        SID_BILLIARDS, 
                        self.get_mt(match["type"]), 
                        1, 
                        0
                    )
                    if go_mid:
                        match['go_mid'] = go_mid
                        # 先清除数据库中已存在的相同go_mid，避免唯一键冲突
                        await self.mysql_db.clear_go_mid_by_value(go_mid)
                        # 然后更新当前记录的go_mid
                        await self.mysql_db.update_match_by_mid(match["mid"], {"go_mid": go_mid})
                        await self.mysql_db.log_bet_items(match)
                        go_mid_map[match['mid']] = go_mid
                        await self.tg_bot.send_message(f"新赛事：[{go_mid}:{match['mid']}] [{match['mtime']}] {match['lname']}@{match['aname']}vs{match['bname']}")
                        await self.match_db.upsert_sport_process({
                            "mid": go_mid,
                            "sid": SID_BILLIARDS,
                            "updateAt": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            "scores": "{}",
                            "extra": "{}",
                            "duration": 0,
                            "version": 1
                        })

        except Exception as e:
            self.logger.exception(f"同步赛事时发生错误: {e}")
        
        return go_mid_map

    def get_mt(self, match_type):
        if match_type == 1:
            return 2
        elif match_type == 2:
            return 3
        return 1
