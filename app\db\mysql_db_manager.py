import json
import logging
from typing import Dict, Any, Set
from sqlalchemy import text, select, update, insert
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.sql import func
from sqlalchemy.engine import Result # 导入 Result 类型
from .models import Match, BetItemLog



class MysqlDBManager:
    def __init__(self, config: dict):
        self.config = config

        self.engine = None
        self.async_session_factory = None
        self.logger = logging.getLogger(self.__class__.__name__)
        self._model_columns_cache: Dict[str, Set[str]] = {} # 新增缓存，键类型改为 str
        self.connect()
        self.logger.info("MysqlDBManager 初始化完成。")
        

    def connect(self):
        try:
            DATABASE_URL = f"mysql+aiomysql://{self.config['user']}:{self.config['password']}@{self.config['host']}/{self.config['database']}"
            self.engine = create_async_engine(DATABASE_URL, echo=False)
            self.async_session_factory = async_sessionmaker(
                self.engine, expire_on_commit=False, class_=AsyncSession
            )
            self.logger.info(f"成功连接到MySQL数据库: {self.config['database']}")
        except Exception as e:
            self.logger.exception(f"MySQL数据库连接错误: {e}")
            self.engine = None
            self.async_session_factory = None

    async def close(self):
        if self.engine:
            await self.engine.dispose()
            self.logger.info("数据库连接池已关闭。")

    def _format_to_dict(self, obj):
        return {c.name: getattr(obj, c.name) for c in obj.__table__.columns}

    def _filter_data_for_model(self, data: Dict[str, Any], model_class: Any) -> Dict[str, Any]: # 类型提示改回 Any
        """
        过滤数据字典，只保留模型中存在的字段。
        """
        model_name = model_class.__name__ # 使用模型类的名称作为缓存键
        if model_name not in self._model_columns_cache:
            self._model_columns_cache[model_name] = {column.name for column in model_class.__table__.columns}
        
        model_columns = self._model_columns_cache[model_name]
        
        filtered_data = {
            key: value for key, value in data.items()
            if key in model_columns
        }
        return filtered_data

    async def log_bet_items(self, match_data: dict):
        if not self.async_session_factory:
            return False
        try:
            # 过滤 match_data，只保留 BetItemLog 存在的字段
            match_data.pop('id', None)
            filtered_match_data = self._filter_data_for_model(match_data, BetItemLog)
            async with self.async_session_factory() as session:
                await session.execute(insert(BetItemLog).values(filtered_match_data))
                await session.commit()
            # self.logger.info(f"成功插入bet_items_logs: mid={match_data['mid']}")
        except Exception as e:
            self.logger.error(f"插入bet_items_logs错误: {e}")



    async def update_match_by_mid(self, mid: int, match_data: dict):
        if not self.async_session_factory: 
            return False
        async with self.async_session_factory() as session:
            try:
                stmt = update(Match).where(Match.mid == mid).values(**match_data)
                result = await session.execute(stmt)
                if result.rowcount == 0:
                    self.logger.warning(f"未找到mid为{mid}的比赛进行更新。")
                    return False
                await session.commit()
                # self.logger.info(f"成功更新比赛数据: mid={mid}")
            except Exception as e:
                self.logger.error(f"更新数据错误: {e}")
                await session.rollback()
                return False

    async def execute_query(self, query, params=None):
        # 此方法通常不直接用于ORM，而是用于执行原始SQL。
        # 如果需要执行原始SQL，可以使用 session.execute(text(query), params)
        # 但更推荐使用ORM查询
        self.logger.warning("execute_query 方法不推荐直接使用，请考虑使用ORM查询。")
        if not self.async_session_factory: 
            return None
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(text(query), params)
                rows = result.fetchall()
                return [row._asdict() for row in rows] # Convert Row objects to dictionaries
            except Exception as e:
                self.logger.error(f"Query execution error: {e}")
                return None

    async def get_match_by_mid(self, mid: int):
        if not self.async_session_factory: 
            return None
        async with self.async_session_factory() as session:
            try:
                match = await session.get(Match, mid)
                if match:
                    return  self._format_to_dict(match) # 返回字典形式
                return None
            except Exception as e:
                self.logger.error(f"查询数据错误: {e}")
                return None

    # 获取已存在的比赛
    async def get_existing_matches_by_mids(self, mids: set) -> dict:
        if not self.async_session_factory or not mids: 
            return {}
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(select(Match).where(Match.mid.in_(mids)))
                rows = result.scalars().all()
                # self.logger.info(f"成功查询到 {len(rows)} 条已存在的比赛数据。")
                return { row.mid: self._format_to_dict(row) for row in rows}
            except Exception as e:
                self.logger.error(f"查询已存在比赛数据错误: {e}")
                return {}
    # 获取未完成的比赛
    async def get_unfinished_matches(self) -> list[dict]:
        if not self.async_session_factory:
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(select(Match).where(Match.type.in_([1, 2])))
                rows = result.scalars().all()
                # self.logger.info(f"成功查询到 {len(rows)} 条未完成的比赛数据。")
                return [self._format_to_dict(row) for row in rows]
            except Exception as e:
                self.logger.error(f"查询未完成比赛数据错误: {e}")
                return []

    # 获取go_mid为null的比赛
    async def get_go_mid_null_matches(self) -> list[dict]: 
        if not self.async_session_factory:  
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match).where(Match.go_mid.is_(None), Match.type < 3, Match.pid == 0)
                )
                matches = result.scalars().all()
                
                self.logger.info(f"查询到 {len(matches)} 条go_mid为null的赛事。")
                return [self._format_to_dict(match) for match in matches]
            except Exception as e:
                self.logger.error(f"查询赛事ID错误: {e}")
                return []

    async def get_go_mid_is_null_matches(self) -> list[dict]: 
        if not self.async_session_factory:
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match).where(Match.type < 3, Match.pid == 0, Match.go_mid.is_(None))
                )
                matches = result.scalars().all()
               
                # self.logger.info(f"查询到 {len(matches)} 条type小于3且go_mid is null的赛事。")
                return [self._format_to_dict(match) for match in matches]
            except Exception as e:
                self.logger.error(f"查询赛事ID错误: {e}")
                return []

    async def get_children_matches_by_mids(self, mid: list[int]) -> list[dict]: # 修改返回类型提示
        if not self.async_session_factory: 
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match).where(Match.pid.in_(mid), Match.type.in_([1, 2]))
                )
                children_matches = result.scalars().all()
                
                # self.logger.info(f"查询到 {len(children_matches)} 条子赛事数据。")
                return [self._format_to_dict(match) for match in children_matches]
            except Exception as e:
                self.logger.error(f"查询子赛事数据错误: {e}")
                return []

    async def bulk_update_status_by_mids(self, mids: list, type_value: int):
        if not self.async_session_factory or not mids: 
            return False
        async with self.async_session_factory() as session:
            try:
                stmt = update(Match).where(Match.mid.in_(mids)).values(type=type_value)
                await session.execute(stmt)
                await session.commit()
                # self.logger.info(f"批量更新 {result.rowcount} 条数据的状态为 {type_value}。")
                return True
            except Exception as e:
                self.logger.error(f"批量更新状态错误: {e}")
                await session.rollback()
                return False

    async def bulk_insert_matches(self, matches_data: list):
        if not self.async_session_factory or not matches_data: 
            return False
        async with self.async_session_factory() as session:
            try:
                # 过滤 Match 模型的字段
                filtered_matches_data = [self._filter_data_for_model(item, Match) for item in matches_data]
                await session.execute(insert(Match).values(filtered_matches_data))

                # 过滤 BetItemLog 模型的字段
                filtered_bet_item_logs_data = [self._filter_data_for_model(item, BetItemLog) for item in matches_data]
                await session.execute(insert(BetItemLog).values(filtered_bet_item_logs_data))

                await session.commit()
                # self.logger.info(f"批量插入 {len(matches_data)} 条比赛数据和日志。")
                return True
            except Exception as e:
                self.logger.error(f"批量插入数据错误: {e}")
                await session.rollback()
                return False

    async def bulk_update_matches(self, matches_data: list):
        if not self.async_session_factory or not matches_data: 
            return False
        async with self.async_session_factory() as session:
            try:
                updated_count = 0
                logs_to_insert = []
                
                for item in matches_data: # item is now {'full_data': ..., 'diff_data': ...}
                    full_match_data = item['full_data']
                    diff_match_data = item['diff_data']

                    # 过滤 Match 模型的字段进行更新
                    update_values = self._filter_data_for_model(diff_match_data, Match)
                    # 移除 mid 和 updated_at，因为它们在 update 语句中通常有特殊处理或自动更新
                    update_values.pop('mid', None)
                    update_values.pop('updated_at', None)

                    stmt = update(Match).where(Match.mid == diff_match_data['mid']).values(**update_values)
                    result = await session.execute(stmt)
                    updated_count += result.rowcount # Count updated rows

                    # 过滤 BetItemLog 模型的字段进行插入
                    logs_to_insert.append(self._filter_data_for_model(full_match_data, BetItemLog))

                if logs_to_insert:
                    await session.execute(insert(BetItemLog).values(logs_to_insert))

                await session.commit()
                # self.logger.info(f"批量更新 {updated_count} 条数据，并记录 {len(logs_to_insert)} 条日志。")
                return True
            except Exception as e:
                self.logger.exception(f"批量更新数据错误: {e}; {json.dumps(matches_data)}")
                await session.rollback()
                return False

    # 获取type为3且result is null的赛事id
    async def get_pending_results(self) -> list[int]:
        if not self.async_session_factory: 
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match.mid).where(Match.type == 3, Match.result.is_(None))
                )
                pending_ids = result.scalars().all()
                # self.logger.info(f"查询到 {len(pending_ids)} 条待处理结果的赛事ID。")
                return list(pending_ids)
            except Exception as e:
                self.logger.error(f"查询待处理结果错误: {e}")
                return []
                
    # 批量更新赛事结果
    async def bulk_update_results(self, results_data: list):
        if not self.async_session_factory or not results_data: 
            return False
        async with self.async_session_factory() as session:
            try:
                updated_rows = 0
                for result_item in results_data:
                    stmt = (
                        update(Match)
                        .where(Match.mid == result_item['mid'], Match.result.is_(None))
                        .values(result=result_item['result'], is_settle=True)
                    )
                    
                    res: Result = await session.execute(stmt) # 添加类型提示
                    updated_rows += res.rowcount
                
                if updated_rows > 0:
                    await session.commit()
                    # self.logger.info(f"批量更新 {updated_rows} 条赛事结果数据。")
                    return True
                else:
                    self.logger.info("没有需要更新的赛事结果数据。")
                    return False
            except Exception as e:
                self.logger.error(f"批量更新赛事结果错误: {e}")
                await session.rollback()
                return False

    # 获取已结束的赛事
    async def get_finished_matches_go_mid(self) -> list[int]:
        if not self.async_session_factory:
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match.go_mid).where(Match.type == 3, Match.go_mid.is_not(None), Match.updated_at >= func.now() - text("INTERVAL 1 DAY"))
                )
                finished_go_mids = result.scalars().all()
                # self.logger.info(f"查询到 {len(finished_go_mids)} 条已结束的赛事。")
                return list(finished_go_mids)
            except Exception as e:
                self.logger.error(f"查询已结束赛事错误: {e}")
                return []
            
    # 获取已有赛果的主赛事未同步到go的赛事
    async def get_un_sync_result_matches(self) -> list[dict]:
        if not self.async_session_factory:
            return []
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match).where(Match.is_settle,Match.result.is_not(None), Match.go_result.is_(False))
                )
                rows = result.scalars().all()
                return [self._format_to_dict(row) for row in rows]
            except Exception as e:
                self.logger.error(f"获取已有赛果的主赛事未同步到go的赛事错误: {e}")
                return []

    
    # 获取go_mid
    async def get_go_mid(self, mid: int) -> int|None:
        if not self.async_session_factory:
            return None
        async with self.async_session_factory() as session:
            try:
                result = await session.execute(
                    select(Match.go_mid).where(Match.mid == mid)
                )
                return result.scalar_one_or_none()
            except Exception as e:
                self.logger.error(f"查询go_mid错误: {e}")
                return None

    # 清除指定go_mid的记录，将其设置为null
    async def clear_go_mid_by_value(self, go_mid: int) -> bool:
        if not self.async_session_factory:
            return False
        async with self.async_session_factory() as session:
            try:
                stmt = update(Match).where(Match.go_mid == go_mid).values(go_mid=None)
                result = await session.execute(stmt)
                await session.commit()
                if result.rowcount > 0:
                    self.logger.info(f"成功清除 {result.rowcount} 条记录的go_mid={go_mid}")
                    return True
                else:
                    self.logger.warning(f"未找到go_mid为{go_mid}的记录进行清除")
                    return False
            except Exception as e:
                self.logger.error(f"清除go_mid错误: {e}")
                await session.rollback()
                return False
