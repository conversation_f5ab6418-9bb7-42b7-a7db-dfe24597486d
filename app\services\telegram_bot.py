import aiohttp
import logging
import asyncio

class TelegramBot:
    def __init__(self, bot_token, chat_id):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.api_url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
        self.logger = logging.getLogger(self.__class__.__name__)

    async def send_message(self, text):
        if not self.bot_token or not self.chat_id or self.bot_token == "YOUR_BOT_TOKEN" or self.chat_id == "YOUR_CHAT_ID":
            self.logger.warning("Telegram bot token or chat_id not configured. Skipping message.")
            return

        payload = {
            'chat_id': self.chat_id,
            'text': f"[V9Crawler]: {text}"
        }
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.api_url, json=payload) as response:
                    response.raise_for_status()
                    self.logger.info("Telegram message sent successfully.")
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            self.logger.error(f"Failed to send Telegram message: {e}")
