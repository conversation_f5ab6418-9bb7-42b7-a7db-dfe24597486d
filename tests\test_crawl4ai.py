import asyncio
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig,ProxyConfig
import re
import json

JSON_REG = re.compile(r"<pre>(\{.*?\})</pre>")

async def main():
    success_count = 0
    fail_count = 0
    # run_config = CrawlerRunConfig(proxy_config={"server":"http://103.107.8.100:8400"})

    proxies = ["http://154.3.37.115:8400",
               "http://154.3.36.109:8400",
               "http://154.3.39.114:8400",
               "http://154.3.38.116:8400",
               "http://154.3.34.18:8400",
               ]
    count = 0
    
    async with Async<PERSON>eb<PERSON>raw<PERSON>() as crawler:
        while True:    
            try:
                count += 1
                result = await crawler.arun(
                    url="https://v9.cool/api/home/<USER>/list?game_id=10&type=1&page=1",
                    # url="https://www.crocs.com/p/classic-clog/10001.html?cgid=women-footwear&cid=6ZW",
                    config=CrawlerRunConfig(
                                            # magic=True,
                                            # simulate_user=True,
                                            # override_navigator=True,
                                            # session_id="my_session123",
                                            verbose=True,
                                            proxy_config=ProxyConfig(server=proxies[count % len(proxies)])
                                            )
                )

                if not result.success: # type:ignore
                    fail_count += 1
                    print(result.error_message) # type:ignore
                    continue
                

                if result.status_code == 200: # type:ignore
                    success_count += 1
                    print(result.cleaned_html) # type:ignore
                    match = JSON_REG.search(result.cleaned_html) # type:ignore
                    if match:
                        json_str = match.group(1)
                        print(json.loads(json_str))
                else:
                    print(f"crawler.arun 状态码错误: {result.status_code}") # type:ignore
                    fail_count += 1
                    
                    
                print(f"success: {success_count}, fail: {fail_count}")
                
            except Exception as e:
                fail_count += 1
                print(f"crawler.arun 发生错误: {e}")
                print(f"success: {success_count}, fail: {fail_count}")

            await asyncio.sleep(3)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("程序被用户中断，正在优雅退出...")
    except Exception as e:
        print(f"发生未预期的错误: {e}")
