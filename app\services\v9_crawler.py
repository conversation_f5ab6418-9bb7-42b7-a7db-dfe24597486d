import asyncio
import logging
import json
from app.db.mysql_db_manager import MysqlDBManager
from crawl4ai import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig, ProxyConfig, CacheMode
import re

class V9Crawler:
    def __init__(self, mysql_db: MysqlDBManager , data_queue, result_queue, telegram_bot, proxies, timeout, v9_urls):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data_queue = data_queue
        self.result_queue = result_queue
        self.telegram_bot = telegram_bot
        self.proxies = proxies
        self.timeout = timeout
        self.v9_urls = v9_urls
        self.mysql_db: MysqlDBManager = mysql_db
        self.fetch_count = 0
        self.proxy_len = len(self.proxies) if self.proxies else 0

        self.crawl4ai = AsyncWebCrawler()
        self.crawl4ai_lock = asyncio.Lock() # Add a lock for crawl4ai operations

        # 数据数量
        self.finish_count = 0
        self.given_count = 0
        self.rolling_count = 0
        self.timeout_count = 0
        self.fetch_success_count = 0
        self.REINITIALIZE_THRESHOLD = 500

    async def _reinitialize_crawler(self):
        self.logger.info("Reinitializing crawl4ai instance...")
        async with self.crawl4ai_lock:
            if self.crawl4ai:
                await self.crawl4ai.close()
            self.crawl4ai = AsyncWebCrawler()
        self.logger.info("crawl4ai instance reinitialized.")

    async def close(self):
        if self.crawl4ai:
            await self.crawl4ai.close()
        

    JSON_REG = re.compile(r"<pre>(\{.*?\})</pre>")

    async def fetch_api(self, api_url, crawl_session_id):
        """
        抓取单个API的数据，包含重试机制。
        """
        MAX_RETRIES = 3
        for attempt in range(MAX_RETRIES):
            try:
                self.fetch_count += 1
                proxy = self.proxies[self.fetch_count % self.proxy_len] if self.proxy_len > 0 else None
                proxy_config = ProxyConfig(server=proxy) if proxy else None
                
                result = await asyncio.wait_for(
                    self.crawl4ai.arun(
                        api_url,
                        config=CrawlerRunConfig(
                            proxy_config=proxy_config,
                            session_id=crawl_session_id,
                            verbose=False,
                            cache_mode=CacheMode.DISABLED,
                            page_timeout=self.timeout * 1000 # Convert seconds to milliseconds
                        )
                    ),
                    timeout=self.timeout + 3 # Timeout in seconds
                )

                if not result.success: # type:ignore
                    error_message = result.error_message # type:ignore
                    self.logger.error(f"抓取 {api_url} 失败: {error_message}; proxy: {proxy}")
                    await self.telegram_bot.send_message(f"抓取 {api_url} 失败: {error_message}; proxy: {proxy}")
                    if ("Target page, context or browser has been closed" in error_message) and attempt < MAX_RETRIES - 1:
                        msg = f"检测到浏览器关闭错误，尝试重新初始化爬虫并重试 ({attempt + 1}/{MAX_RETRIES})..."
                        self.logger.warning(msg)
                        await self.telegram_bot.send_message(msg)
                        await self._reinitialize_crawler()
                        await asyncio.sleep(1) # Give some time for resources to release
                        continue # Retry
                    # The retry logic will be handled by the outer except block
                    return None

                if result.status_code != 200: # type:ignore
                    self.logger.error(f"抓取 {api_url} 状态码错误: {result.status_code}; proxy: {proxy}") # type:ignore
                    await self.telegram_bot.send_message(f"抓取 {api_url} 状态码错误: {result.status_code}; proxy: {proxy}") # type:ignore
                    return None

                body = result.cleaned_html # type:ignore
                match = self.JSON_REG.search(body)
                if match:
                    json_str = match.group(1)
                    json_data = json.loads(json_str)
                    self.logger.info(f"成功抓取: {api_url}; proxy: {proxy}")
                    
                    # 成功抓取后增加计数器，并检查是否需要重新初始化
                    self.fetch_success_count += 1
                    if self.fetch_success_count >= self.REINITIALIZE_THRESHOLD:
                        self.logger.info(f"达到重新初始化阈值 ({self.REINITIALIZE_THRESHOLD})，重新初始化 crawl4ai 实例...")
                        await self._reinitialize_crawler()
                        self.fetch_success_count = 0 # 重置计数器

                    return json_data
                self.logger.error(f"未找到JSON数据: {api_url}, {body}")
                await self.telegram_bot.send_message(f"未找到JSON数据: {api_url}, {body}")
                return None

            except asyncio.CancelledError:
                self.logger.info(f"抓取任务被取消: {api_url}")
                raise
            except asyncio.TimeoutError:
                self.logger.error(f"抓取 {api_url} 超时")
                await self.telegram_bot.send_message(f"抓取 {api_url} 超时")
                return None
            except Exception as e:
                error_message = str(e)
                if ("Target page, context or browser has been closed" in error_message) and attempt < MAX_RETRIES - 1:
                    msg = f"检测到浏览器关闭错误，尝试重新初始化爬虫并重试 ({attempt + 1}/{MAX_RETRIES})..."
                    self.logger.warning(msg)
                    await self.telegram_bot.send_message(msg)
                    await self._reinitialize_crawler()
                    await asyncio.sleep(1) # Give some time for resources to release
                    continue # Retry
                
                self.logger.exception(f"抓取 {api_url} 时发生错误: {e}")
                await self.telegram_bot.send_message(f"抓取 {api_url} 时发生错误: {e}")
                return None
        return None # All retries failed
        
    
    async def _fetch_matches(self, url):
        """
        内部方法，用于轮询抓取比赛数据。
        """
        page = 1
        all_data = []
        try:
            while True:
                page_data = await self.fetch_api(f"{url}{page}", "v9_matches")
                if not page_data:
                    break
                
                data = page_data.get("data", {})
                if not data:
                    break

                self.given_count = data.get("given", 0)
                self.rolling_count = data.get("rolling", 0)
                self.finish_count = data.get("finish", 0)
                
                current_list = data.get("list", [])
                if not current_list:
                    break
                
                all_data.extend(current_list)
                
                if len(current_list) < 10:
                    break
                page += 1
                
                # 在每次循环中检查取消信号
                current_task = asyncio.current_task()
                if current_task and current_task.cancelled():
                    raise asyncio.CancelledError()
                    
        except asyncio.CancelledError:
            self.logger.info(f"_fetch_matches 任务被取消: {url}")
            raise
            
        return all_data

    async def start_matches_crawling(self):
        """
        启动爬虫主循环
        """
        self.logger.info("matches 爬虫任务启动。")
        interval = self.v9_urls.get("interval", 3)
        given_url = self.v9_urls.get("given", "")
        rolling_url = self.v9_urls.get("rolling", "")
        if not given_url or not rolling_url:    
            self.logger.error("配置中未找到有效的API URL。")
            return

        try:
            continue_time = 0
            while True:
                fetched_data = []
                tasks = []
                
                try:
                    if self.given_count > 0 or self.rolling_count == 0:
                        given_data = await self._fetch_matches(given_url)
                        if given_data:
                            given_data = [{**item, "type": 1} for item in given_data]
                            fetched_data.extend(given_data)
                    if self.rolling_count > 0:
                        rolling_data = await self._fetch_matches(rolling_url)
                        if rolling_data:
                            rolling_data = [{**item, "type": 2} for item in rolling_data]
                            fetched_data.extend(rolling_data)

                except asyncio.CancelledError:
                    self.logger.info("matches 爬虫任务被取消。")
                    # 取消所有未完成的任务
                    for task in tasks:
                        if not task.done():
                            task.cancel()
                    raise

                if len(fetched_data) == self.given_count + self.rolling_count:
                    continue_time = 0
                    self.logger.info(f"given_count: {self.given_count}, rolling_count: {self.rolling_count}, finish_count: {self.finish_count}")
                    await self.data_queue.put(fetched_data)
                else:
                    self.logger.warning(f"fetched_data 长度不匹配: {len(fetched_data)} != {self.given_count + self.rolling_count}")
                    if continue_time < 2:
                        continue_time += 1
                        continue


                # 使用可中断的睡眠
                try:
                    
                    await asyncio.sleep(interval)
                except asyncio.CancelledError:
                    self.logger.info("matches 爬虫任务在睡眠期间被取消。")
                    raise
                    
        except asyncio.CancelledError:
            self.logger.info("matches 爬虫任务已停止。")
            raise


    async def start_results_crawling(self):
        """
        启动爬虫主循环，等待赛事打完后再抓取结果数据。
        """
        self.logger.info("results 爬虫任务启动。")

        url = self.v9_urls.get("finish", "")
        if not url:
            self.logger.error("配置中未找到有效的API URL。")
            return

        try:
            while True:
                pending_result_mids = await self.mysql_db.get_pending_results()
                
                if pending_result_mids:
                    result = []
                    min_id = min(pending_result_mids)
                    self.logger.info(f"准备获取结果id: {json.dumps(pending_result_mids)}")
                    page = 1
                    
                    try:
                        while True:
                            data = await self.fetch_api(f"{url}{page}", "v9_results")
                            if not data:
                                self.logger.error(f"获取结果数据失败或无数据: 第{page}页")
                                break
                            list = data.get("data", {}).get("list", [])
                            if not list:
                                self.logger.info(f"列表数据为空: 第{page}页")
                                break
                            
                            self.logger.info(f"获取到结果数据: 第{page}页")
                            for item in list:
                                if item["id"] in pending_result_mids:
                                    self.logger.info(f"找到目标结果: {item["id"]}")
                                    a_score = item.get("a_jushu") if item.get("a_jushu")  != "" else 0
                                    b_score = item.get("b_jushu") if item.get("b_jushu")  != "" else 0
                                    result.append({
                                        "mid": item["id"],
                                        "result": f"{a_score}:{b_score}"
                                    })
                            
                            if len(result) == len(pending_result_mids) or list[-1]["id"] < min_id:
                                if len(result) > 0:
                                    await self.mysql_db.bulk_update_results(result)
                                break
                                
                            page += 1
                            
                            # 在每次循环中检查取消信号
                            current_task = asyncio.current_task()
                            if current_task and current_task.cancelled():
                                raise asyncio.CancelledError()
                                
                    except asyncio.CancelledError:
                        self.logger.info(f"results 爬虫在处理ID {id} 时被取消。")
                        # 确保标记任务完成，避免队列阻塞
                        self.result_queue.task_done()
                        raise
                    except Exception as e:
                        self.logger.exception(f"处理结果数据时发生错误: {e}")

                await asyncio.sleep(10)
                
        except asyncio.CancelledError:
            self.logger.info("results 爬虫任务已停止。")
            raise
        except Exception as e:
            self.logger.exception(f"results 爬虫任务发生错误: {e}")
            raise
