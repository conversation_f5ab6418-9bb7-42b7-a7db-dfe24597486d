services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    network_mode: host
    volumes:
      - ../app:/app
    environment:
      TZ: Asia/Shanghai 
    depends_on:
      - db
    mem_limit: 1g
    init: true
    restart: always

  db: 
    image: mysql:8.4 
    network_mode: host 
    environment:
      TZ: Asia/Shanghai 
      MYSQL_ROOT_PASSWORD: VLBkKctRFdWQYHlX 
      MYSQL_DATABASE: china_billiards 
      MYSQL_USER: user 
      MYSQL_PASSWORD: 4apD25Qrk0KMJt9f 
    volumes:
      - ../mysql_db_data:/var/lib/mysql 
    command: --default-time-zone='+08:00'
    restart: always


