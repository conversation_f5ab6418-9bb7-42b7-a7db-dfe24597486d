from sqlalchemy.orm import declarative_base
from sqlalchemy import (Column, Integer, DateTime,  func, BIGINT, NVARCHAR,  UniqueConstraint, String, JSON, Boolean)
from sqlalchemy.dialects.mssql import TINYINT, BIT

Base = declarative_base()

# --- Models for 'match' database ---
class SportLeagueName(Base):
    __tablename__ = 'sport_league_name'
    __table_args__ = {'schema': 'dbo'}
    id = Column(BIGINT, primary_key=True, autoincrement=False)
    sid = Column(TINYINT, nullable=False)
    lid = Column(Integer, nullable=False)
    lang = Column(TINYINT, nullable=False)
    name = Column(NVARCHAR(300), nullable=False)
    logo = Column(BIT, nullable=False, default=False)
    level = Column(TINYINT, nullable=False, default=100)
    state = Column(TINYINT, nullable=False, default=1)
    chot = Column(BIT, nullable=False, default=True)
    origin_datasrc = Column(NVARCHAR(10), nullable=False, default="v9")

class SportTeamName(Base):
    __tablename__ = 'sport_team_name'
    __table_args__ = {'schema': 'dbo'}
    id = Column(BIGINT, primary_key=True, autoincrement=False)
    sid = Column(TINYINT, nullable=False)
    tid = Column(Integer, nullable=False)
    lang = Column(TINYINT, nullable=False)
    name = Column(NVARCHAR(300), nullable=False)
    state = Column(TINYINT, nullable=False, default=1)
    logo = Column(BIT, nullable=False, default=False)
    cupdate = Column(BIT, nullable=False, default=True)
    origin_datasrc = Column(NVARCHAR(10), nullable=False, default="v9")

class SportMatch(Base):
    __tablename__ = 'sport_match'
    __table_args__ = {'schema': 'dbo'}
    mid = Column(Integer, primary_key=True, autoincrement=False)
    sid = Column(TINYINT, nullable=False)
    lid = Column(Integer, nullable=False)
    aid = Column(Integer, nullable=False)
    bid = Column(Integer, nullable=False)
    mt = Column(TINYINT, nullable=False, default=1)
    parlay = Column(BIT, nullable=False, default=False)
    mtime = Column(DateTime, nullable=False)
    gcount = Column(Integer, nullable=False, default=0)
    running = Column(BIT, nullable=False, default=False)
    state = Column(TINYINT, nullable=False, default=1)
    copen = Column(TINYINT, nullable=False, default=1)
    cupdate = Column(TINYINT, nullable=False, default=1)
    sstate = Column(TINYINT, nullable=False, default=0)
    rstate = Column(TINYINT, nullable=False, default=0)
    atime = Column(DateTime, nullable=True)
    updateAt = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())

class SportResultCrawl(Base):
    __tablename__ = 'sport_result_crawl'
    __table_args__ = (
        UniqueConstraint('mid', 'rid', 'hcp', name='uk_sport_result_crawl'),
        {'schema': 'dbo'}
    )
    id = Column(Integer, primary_key=True, autoincrement=True)
    uk = Column(NVARCHAR(50), nullable=False)
    mid = Column(Integer, nullable=False)
    sid = Column(TINYINT, nullable=False)
    rid = Column(Integer, nullable=False)
    rcode = Column(NVARCHAR(20), nullable=False)
    hcp = Column(Integer, nullable=False, default=0)
    a = Column(Integer, nullable=False, default=0)
    b = Column(Integer, nullable=False, default=0)
    c = Column(NVARCHAR(500), nullable=False, default='')
    type = Column(TINYINT, nullable=False)
    state = Column(TINYINT, nullable=False)
    createAt = Column(DateTime, nullable=False, default=func.now())
    updateAt = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())


# --- Model for 'log' database ---
class OddLogs(Base):
    __tablename__ = 'odd_logs'
    __table_args__ = {'schema': 'dbo'}
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    matchId = Column(BIGINT, nullable=False)
    score = Column(NVARCHAR(100))
    proceedTime = Column(NVARCHAR(30))
    game = Column(NVARCHAR(100))
    plate = Column(NVARCHAR(100))
    hcp = Column(NVARCHAR(30))
    odds = Column(NVARCHAR, nullable=False)
    type = Column(TINYINT, nullable=False)
    updateAt = Column(DateTime, nullable=False)
    createAt = Column(DateTime, nullable=False, default=func.now())
    src = Column(TINYINT, nullable=False, default=0)
    sid = Column(TINYINT)




# 定义 Match 模型
class Match(Base):
    __tablename__ = 'matches'
    id = Column(Integer, primary_key=True, autoincrement=True)
    game_id = Column(Integer, nullable=True)
    mid = Column(Integer)
    go_mid = Column(Integer, nullable=True) 
    lname = Column(String(255))
    aname = Column(String(255))
    bname = Column(String(255))
    mtime = Column(DateTime)
    type = Column(Integer)
    is_pan = Column(Boolean)
    is_settle = Column(Boolean)
    pid = Column(Integer, nullable=True)
    bet_items = Column(JSON)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    result = Column(String(255), nullable=True)
    go_result = Column(Boolean)
    def __repr__(self):
        return f"<Match(mid={self.mid}, lname='{self.lname}')>"

# 定义 BetItemLog 模型
class BetItemLog(Base):
    __tablename__ = 'bet_items_logs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    game_id = Column(Integer, nullable=True)
    mid = Column(Integer)
    go_mid = Column(Integer, nullable=True) 
    lname = Column(String(255))
    aname = Column(String(255))
    bname = Column(String(255))
    mtime = Column(DateTime)
    type = Column(Integer)
    is_pan = Column(Boolean)
    is_settle = Column(Boolean)
    pid = Column(Integer, nullable=True)
    bet_items = Column(JSON)

    def __repr__(self):
        return f"<BetItemLog(id={self.id}, mid={self.mid})>"

class SportProcess(Base):
    __tablename__ = 'sport_process'
    __table_args__ = {'schema': 'dbo'}
    mid = Column(Integer, primary_key=True, autoincrement=False)
    sid = Column(TINYINT, nullable=False)
    type = Column(TINYINT, nullable=False, default=0)
    time = Column(String(20), nullable=False, default='')
    ascore = Column(Integer, nullable=False, default=0)
    bscore = Column(Integer, nullable=False, default=0)
    ared = Column(Integer, nullable=False, default=0)
    bred = Column(Integer, nullable=False, default=0)
    extra = Column(String(2000))
    scores = Column(String(2000))
    duration = Column(Integer, nullable=False)
    version = Column(Integer, nullable=False)
    updateAt = Column(DateTime, nullable=False, onupdate=func.now())
    scoreAt = Column(DateTime)
    ttype = Column(TINYINT, nullable=False, default=0)
    ttime = Column(DateTime)
