import json
import os

# --- Start of Change ---
# Get the absolute path of the directory where this script is located
_CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

def _load_config(config_filename='config.json'):
    """
    Loads the configuration file from the same directory as this script.
    """
    config_path = os.path.join(_CURRENT_DIR, config_filename)
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found at {config_path}")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config
# --- End of Change ---

# Load the configuration once when the module is imported.
config = _load_config()


def get_v9_config():
    """
    获取v9爬虫的配置。
    """
    return config.get('v9', {})

def get_proxies():
    """
    获取代理服务器列表。
    """
    return config.get('proxies', [])

def get_mysql_config():
    """
    获取MySQL数据库的配置。
    """
    return config.get('mysql', {})

def get_redis_config():
    """
    获取Redis的配置。
    """
    return config.get('redis', {})

def get_telegram_config():
    """
    获取Telegram Bot的配置。
    """
    return config.get('telegram', {})

def get_odbc_config():
    """
    获取ODBC连接字符串。
    """
    return config.get('odbc', {})

def get_bet_items_id_mapping():
    """
    获取投注项ID映射。
    """
    return config.get('bet_items_id_mapping', {})

def get_timeout():
    """
    获取全局超时设置。
    """
    return config.get('timeout', 10)
def get_result_id_mapping():
    """
    获取赛果id映射
    """
    return config.get('result_id_mapping', {})


if __name__ == '__main__':
    # 示例：打印所有配置
    print("V9 Config:", get_v9_config())
    print("Proxies:", get_proxies())
    print("MySQL Config:", get_mysql_config())
    print("Redis Config:", get_redis_config())
    print("Telegram Config:", get_telegram_config())
    print("ODBC Config:", get_odbc_config())
    print("Bet Items ID Mapping:", get_bet_items_id_mapping())
    print("Timeout:", get_timeout())
