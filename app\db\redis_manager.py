import logging
import redis.asyncio as aioredis


class RedisManager:
    """
    Manages Redis connections and operations using aioredis.
    """
    def __init__(self, config):
        """
        Initializes the RedisManager with connection details from config.
        """
        self.redis = None
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

    async def connect(self) -> None:
        """
        Establishes a connection to the Redis server.
        """
        try:
            self.redis = aioredis.Redis(host=self.config['host'], port=self.config['port'], db=self.config['db'], password=self.config['password'], decode_responses=True)
            await self.redis.ping()
            self.logger.info("Successfully connected to Redis.")
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.redis = None

    async def close(self) -> None:
        """
        Closes the Redis connection.
        """
        if self.redis:
            await self.redis.aclose()
            self.logger.info("Redis connection closed.")

    async def get_hash(self, hash_name: str) -> dict:
        """
        Gets all fields and values from a Redis hash.

        Args:
            hash_name: The name of the hash.

        Returns:
            A dictionary of field-value pairs.
        """
        if not self.redis:
            self.logger.error("Redis connection not available.")
            return {}
        try:
            return await self.redis.hgetall(hash_name) # type: ignore
        except Exception as e:
            self.logger.error(f"Failed to get hash '{hash_name}': {e}")
            return {}

    async def batch_set_hash(self, hash_name: str, data: dict) -> None:
        """
        Sets multiple fields in a Redis hash in a single operation.

        Args:
            hash_name: The name of the hash.
            data: A dictionary of field-value pairs to set.
        """
        if not self.redis:
            self.logger.error("Redis connection not available.")
            return

        try:
            await self.redis.hset(hash_name, mapping=data) # type: ignore
            # self.logger.info(f"Successfully set {len(data)} fields in hash '{hash_name}'.")
        except Exception as e:
            self.logger.error(f"Failed to set hash fields for '{hash_name}': {e}")

