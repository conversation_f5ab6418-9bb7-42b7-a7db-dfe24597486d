# 中国台球爬虫项目 (China Billiards Crawler)

这是一个基于 Python `asyncio` 实现的异步爬虫项目，用于并发抓取特定 API 的数据，并进行异步处理和入库。项目设计旨在高效、稳定地从受 Cloudflare 防护的网站抓取数据。

## 项目功能

*   **并发爬取**: 支持同时从多个 API 端点抓取数据。
*   **异步处理**: 爬虫和数据处理模块独立运行，通过队列进行数据传输，提高整体效率。
*   **Cloudflare 防护绕过**: 使用 `cloudscraper` 库处理 Cloudflare 的反爬机制。
*   **定时抓取**: 每隔指定时间（默认为 3 秒）执行一次数据抓取。
*   **日志记录**: 详细的日志输出，方便监控程序运行状态和问题排查。
*   **配置化**: 通过 `config.json` 文件管理 API 地址、超时时间等配置。

## 文件结构

```
.
├── config.json             # 项目配置文件，包含 API 地址、超时等
├── config_loader.py        # 配置文件加载模块
├── data_process.py         # 数据处理模块，负责数据清洗、转换和入库
├── main.py                 # 程序入口，负责初始化和启动爬虫与数据处理任务
├── README.md               # 项目说明文档
├── v9_crawler.py           # 爬虫核心模块，负责数据抓取和 Cloudflare 绕过
└── .gitignore              # Git 忽略文件，用于排除不需要版本控制的文件
```

## 玩法
```json
{
    "betItems": {
        // 让局
        "HandicapRound": [ 
            {"hcp": "2.5", "a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0},
            {"hcp": "3.5", "a": 1.93, "b": 2.82, "a_lock": 0, "b_lock": 0}
        ],
        // 让分
        "HandicapPoint": [ 
            {"hcp": "2.5", "a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0},
            {"hcp": "3.5", "a": 1.93, "b": 2.82, "a_lock": 0, "b_lock": 0}
        ],
        // 独赢
        "FullWinner": [{"a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0}],
        
        // 总局数单双
        "TotalRoundOE": [{"a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0}],
        // 总分单双
        "TotalPointOE": [{"a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0}],
        // 主队第x局大小球
        "HomeRoundXOU": [{"plate": 1, "hcp": "1", "a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0}],
        // 客队第x局大小球
        "GuestRoundXOU": [{"plate": 1, "hcp": "1", "a": 1.55, "b": 2.15, "a_lock": 0, "b_lock": 0}]
    }
}
```