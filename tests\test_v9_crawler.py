import pytest
import asyncio
import logging
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from app.services.v9_crawler import V9Crawler
from app.db.mysql_db_manager import MysqlDBManager
from crawl4ai import AsyncWebCrawler

# Mock the logger to prevent actual log output during tests
@pytest.fixture(autouse=True)
def mock_logger():
    logging.disable(logging.CRITICAL)
    yield
    logging.disable(logging.NOTSET)

@pytest_asyncio.fixture
async def v9_crawler_with_mocks():
    mock_mysql_db = AsyncMock(spec=MysqlDBManager)
    mock_data_queue = asyncio.Queue()
    mock_result_queue = asyncio.Queue()
    mock_telegram_bot = AsyncMock()
    mock_proxies = ["http://proxy1.com", "http://proxy2.com"]
    mock_timeout = 10
    mock_v9_urls = {"given": "http://example.com/given?page=", "rolling": "http://example.com/rolling?page=", "finish": "http://example.com/finish?page="}

    # Patch AsyncWebCrawler class
    with patch('app.services.v9_crawler.AsyncWebCrawler') as MockAsyncWebCrawler:
        # Configure the mock instance that will be returned when AsyncWebCrawler() is called
        mock_crawl4ai_instance = AsyncMock(spec=AsyncWebCrawler)
        MockAsyncWebCrawler.return_value = mock_crawl4ai_instance
        
        crawler = V9Crawler(
            mysql_db=mock_mysql_db,
            data_queue=mock_data_queue,
            result_queue=mock_result_queue,
            telegram_bot=mock_telegram_bot,
            proxies=mock_proxies,
            timeout=mock_timeout,
            v9_urls=mock_v9_urls
        )
        # The crawler's crawl4ai attribute is already the mock instance due to the patch
        crawler.logger = MagicMock() # Mock the logger for assertions

        yield crawler, mock_mysql_db, mock_data_queue, mock_result_queue, mock_telegram_bot, mock_crawl4ai_instance, MockAsyncWebCrawler

        # Ensure crawl4ai.close is called if it was mocked
        if mock_crawl4ai_instance.close.called:
            await mock_crawl4ai_instance.close()


@pytest.mark.asyncio
async def test_fetch_api_success(v9_crawler_with_mocks):
    crawler, _, _, _, mock_telegram_bot, mock_crawl4ai_instance, _ = v9_crawler_with_mocks
    
    # Mock a successful response from crawl4ai.arun
    mock_crawl4ai_instance.arun.return_value = MagicMock(
        success=True,
        status_code=200,
        cleaned_html='<pre>{"code": 0, "data": {"list": []}}</pre>'
    )

    api_url = "http://test.com/api"
    result = await crawler.fetch_api(api_url)

    assert result == {"code": 0, "data": {"list": []}}
    mock_crawl4ai_instance.arun.assert_called_once()
    crawler.logger.info.assert_called_with(f"成功抓取: {api_url}")
    mock_telegram_bot.send_message.assert_not_called()

@pytest.mark.asyncio
async def test_fetch_api_failure_crawl4ai_error(v9_crawler_with_mocks):
    crawler, _, _, _, mock_telegram_bot, mock_crawl4ai_instance, _ = v9_crawler_with_mocks
    
    # Mock a failed response from crawl4ai.arun
    mock_crawl4ai_instance.arun.return_value = MagicMock(
        success=False,
        error_message="Network error",
        status_code=None, # Not applicable for success=False
        cleaned_html=None
    )

    api_url = "http://test.com/api"
    result = await crawler.fetch_api(api_url)

    assert result is None
    mock_crawl4ai_instance.arun.assert_called_once()
    crawler.logger.error.assert_called_with(f"抓取 {api_url} 失败: Network error")
    mock_telegram_bot.send_message.assert_called_with(f"抓取 {api_url} 失败: Network error")

@pytest.mark.asyncio
async def test_fetch_api_failure_status_code(v9_crawler_with_mocks):
    crawler, _, _, _, mock_telegram_bot, mock_crawl4ai_instance, _ = v9_crawler_with_mocks
    
    # Mock a response with non-200 status code
    mock_crawl4ai_instance.arun.return_value = MagicMock(
        success=True,
        status_code=404,
        cleaned_html='Page not found'
    )

    api_url = "http://test.com/api"
    result = await crawler.fetch_api(api_url)

    assert result is None
    mock_crawl4ai_instance.arun.assert_called_once()
    crawler.logger.error.assert_called_with(f"抓取 {api_url} 状态码错误: 404")
    mock_telegram_bot.send_message.assert_called_with(f"抓取 {api_url} 状态码错误: 404")

@pytest.mark.asyncio
async def test_fetch_api_no_json(v9_crawler_with_mocks):
    crawler, _, _, _, mock_telegram_bot, mock_crawl4ai_instance, _ = v9_crawler_with_mocks
    
    # Mock a response with no JSON in cleaned_html
    mock_crawl4ai_instance.arun.return_value = MagicMock(
        success=True,
        status_code=200,
        cleaned_html='<html><body>No JSON here</body></html>'
    )

    api_url = "http://test.com/api"
    result = await crawler.fetch_api(api_url)

    assert result is None
    mock_crawl4ai_instance.arun.assert_called_once()
    crawler.logger.error.assert_called_with(f"未找到JSON数据: {api_url}, <html><body>No JSON here</body></html>")
    mock_telegram_bot.send_message.assert_called_with(f"未找到JSON数据: {api_url}, <html><body>No JSON here</body></html>")

@pytest.mark.asyncio
async def test_fetch_api_browser_closed_retry(v9_crawler_with_mocks):
    crawler, _, _, _, mock_telegram_bot, mock_crawl4ai_instance, MockAsyncWebCrawler = v9_crawler_with_mocks
    
    # Simulate browser closed error on first attempt, then success on second
    mock_crawl4ai_instance.arun.side_effect = [
        MagicMock(
            success=False,
            error_message="Target page, context or browser has been closed",
            status_code=None,
            cleaned_html=None
        ),
        MagicMock(
            success=True,
            status_code=200,
            cleaned_html='<pre>{"code": 0, "data": {"list": []}}</pre>'
        )
    ]

    # Ensure close method is mocked to return a coroutine
    mock_crawl4ai_instance.close.return_value = None # Or AsyncMock()

    api_url = "http://test.com/api"
    result = await crawler.fetch_api(api_url)

    assert result == {"code": 0, "data": {"list": []}}
    assert mock_crawl4ai_instance.arun.call_count == 2 # Called twice due to retry
    crawler.logger.warning.assert_called_with("检测到浏览器关闭错误，尝试重新初始化爬虫并重试 (1/3)...")
    mock_telegram_bot.send_message.assert_called_once_with(f"抓取 {api_url} 失败: Target page, context or browser has been closed")
    assert MockAsyncWebCrawler.call_count == 2 # Verify AsyncWebCrawler was instantiated again
    mock_crawl4ai_instance.close.assert_called_once() # Verify close was called on the original instance

@pytest.mark.asyncio
async def test_fetch_api_exception_retry(v9_crawler_with_mocks):
    crawler, _, _, _, mock_telegram_bot, mock_crawl4ai_instance, MockAsyncWebCrawler = v9_crawler_with_mocks
    
    # Simulate an exception on first attempt, then success on second
    mock_crawl4ai_instance.arun.side_effect = [
        Exception("Some unexpected error"),
        MagicMock(
            success=True,
            status_code=200,
            cleaned_html='<pre>{"code": 0, "data": {"list": []}}</pre>'
        )
    ]

    # Ensure close method is mocked to return a coroutine
    mock_crawl4ai_instance.close.return_value = None # Or AsyncMock()

    api_url = "http://test.com/api"
    result = await crawler.fetch_api(api_url)

    assert result is None
    assert mock_crawl4ai_instance.arun.call_count == 1 # Called once, no retry for generic exception
    crawler.logger.exception.assert_called_once_with(f"抓取 {api_url} 时发生错误: Some unexpected error")
    mock_telegram_bot.send_message.assert_called_once_with(f"抓取 {api_url} 时发生错误: Some unexpected error")
    crawler.logger.warning.assert_not_called() # No browser closed warning for generic exception
    MockAsyncWebCrawler.assert_called_once() # Verify AsyncWebCrawler was instantiated once
    mock_crawl4ai_instance.close.assert_not_called() # Close is not called if no retry

@pytest.mark.asyncio
async def test_fetch_api_cancelled_error(v9_crawler_with_mocks):
    crawler, _, _, _, _, mock_crawl4ai_instance, _ = v9_crawler_with_mocks
    
    # Simulate CancelledError
    mock_crawl4ai_instance.arun.side_effect = asyncio.CancelledError

    api_url = "http://test.com/api"
    
    with pytest.raises(asyncio.CancelledError):
        await crawler.fetch_api(api_url)

    mock_crawl4ai_instance.arun.assert_called_once()
    crawler.logger.info.assert_called_with(f"抓取任务被取消: {api_url}")
