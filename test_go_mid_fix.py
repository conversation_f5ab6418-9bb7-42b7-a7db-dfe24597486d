#!/usr/bin/env python3
"""
测试脚本：验证 go_mid 唯一键冲突修复
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.mysql_db_manager import MysqlDBManager
from app.config import load_config

async def test_clear_go_mid():
    """测试清除 go_mid 的功能"""
    config = load_config()
    mysql_db = MysqlDBManager(config['mysql'])
    
    # 测试的 go_mid 值
    test_go_mid = 988000920
    
    print(f"测试清除 go_mid={test_go_mid}")
    
    try:
        # 执行清除操作
        result = await mysql_db.clear_go_mid_by_value(test_go_mid)
        
        if result:
            print(f"✅ 成功清除 go_mid={test_go_mid}")
        else:
            print(f"⚠️  未找到 go_mid={test_go_mid} 的记录或清除失败")
            
    except Exception as e:
        print(f"❌ 清除 go_mid 时发生错误: {e}")
    
    finally:
        await mysql_db.close()

async def test_update_after_clear():
    """测试清除后更新的完整流程"""
    config = load_config()
    mysql_db = MysqlDBManager(config['mysql'])
    
    # 测试数据
    test_go_mid = 988000920
    test_mid = 5406  # 从错误信息中获取的 mid
    
    print(f"测试完整流程：清除 go_mid={test_go_mid}，然后更新 mid={test_mid}")
    
    try:
        # 1. 先清除可能存在的 go_mid
        clear_result = await mysql_db.clear_go_mid_by_value(test_go_mid)
        print(f"清除结果: {clear_result}")
        
        # 2. 然后更新指定记录的 go_mid
        update_result = await mysql_db.update_match_by_mid(test_mid, {"go_mid": test_go_mid})
        print(f"更新结果: {update_result}")
        
        if update_result is not False:
            print("✅ 完整流程测试成功")
        else:
            print("❌ 更新失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        await mysql_db.close()

if __name__ == "__main__":
    print("开始测试 go_mid 唯一键冲突修复...")
    
    # 运行测试
    asyncio.run(test_clear_go_mid())
    print("-" * 50)
    asyncio.run(test_update_after_clear())
    
    print("测试完成！")
