# 使用官方 Python 运行时作为父镜像
FROM python:3.13.5-slim-bookworm

# 安装 Microsoft ODBC Driver for SQL Server
RUN apt-get update && apt-get install -y curl gnupg \
    && curl -sSL -O https://packages.microsoft.com/config/debian/$(grep VERSION_ID /etc/os-release | cut -d '"' -f 2 | cut -d '.' -f 1)/packages-microsoft-prod.deb \
    && dpkg -i packages-microsoft-prod.deb \
    && rm packages-microsoft-prod.deb \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql17 unixodbc-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 requirements.txt 并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

RUN crawl4ai-setup

# 运行 main.py
CMD ["python", "main.py"]
