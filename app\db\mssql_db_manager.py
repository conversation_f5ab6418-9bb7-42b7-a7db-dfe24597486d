import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import (insert, select, func,update,case,text)
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from .models import SportLeagueName, SportTeamName, SportMatch, OddLogs, SportProcess

class BaseDatabaseManager:
    def __init__(self, db_key: str, config: dict):
        # self.logger = logging.getLogger(f"{__name__}.{db_key}")
        self.logger = logging.getLogger(self.__class__.__name__)

        db_config = config[db_key]
        async_connection_url = f"mssql+aioodbc:///?odbc_connect={db_config}"

        self.engine = create_async_engine(async_connection_url, echo=False)
        self.async_session_factory = async_sessionmaker(bind=self.engine, class_=AsyncSession, expire_on_commit=False)
        self.logger.info(f"DatabaseManager for '{db_key}' initialized.")
    
    async def close(self):
        if self.engine:
            await self.engine.dispose()

    def format_to_dict(self, obj):
        return {c.name: getattr(obj, c.name) for c in obj.__table__.columns}


class MatchDatabaseManager(BaseDatabaseManager):
    def __init__(self, config: dict):
        super().__init__('match', config)

    async def _get_next_seq_value(self, session: AsyncSession, seq_name: str) -> int:
        result = await session.execute(text(f"SELECT NEXT VALUE FOR {seq_name}"))
        return result.scalar_one()

    async def get_or_create_league(self, session: AsyncSession, league_name: str, sid: int) -> int:
        stmt = select(SportLeagueName.lid).where(SportLeagueName.name == league_name).limit(1)
        result = await session.execute(stmt)
        existing_lid = result.scalars().first()
        if existing_lid and existing_lid % 100 == 88:
            self.logger.info(f"Found existing league: '{league_name}', lid: {existing_lid}")
            return existing_lid
        self.logger.info(f"League not found: '{league_name}', creating...")
        new_lid = await self._get_next_seq_value(session, "dbo.seq_league_88")
        new_lid = new_lid * 100 + 88
        for lang_id in [1, 2, 3]:
            new_id = new_lid * 100 + lang_id
            session.add(SportLeagueName(id=new_id, sid=sid, lid=new_lid, lang=lang_id, name=league_name))
        await session.flush()
        self.logger.info(f"Successfully created league: '{league_name}', lid: {new_lid}")
        return new_lid

    async def get_or_create_team(self, session: AsyncSession, team_name: str, sid: int) -> int:
        stmt = select(SportTeamName.tid).where(SportTeamName.name == team_name).limit(1)
        result = await session.execute(stmt)
        existing_tid = result.scalars().first()
        if existing_tid and existing_tid % 100 == 88:
            self.logger.info(f"Found existing team: '{team_name}', tid: {existing_tid}")
            return existing_tid
        self.logger.info(f"Team not found: '{team_name}', creating...")
        new_tid = await self._get_next_seq_value(session, "dbo.seq_team_88")
        new_tid = new_tid * 100 + 88
        for lang_id in [1, 2, 3]:
            new_id = new_tid * 100 + lang_id
            session.add(SportTeamName(id=new_id, sid=sid, tid=new_tid, lang=lang_id, name=team_name))
        await session.flush()
        self.logger.info(f"Successfully created team: '{team_name}', tid: {new_tid}")
        return new_tid

    async def create_match(self, session: AsyncSession, sid: int, lid: int, home_tid: int, away_tid: int, match_time: datetime, mt: int, state: int, gcount: int) -> int:
        self.logger.info(f"Creating match: lid={lid}, home_tid={home_tid}, away_tid={away_tid}")
        base_mid = await self._get_next_seq_value(session, "dbo.seq_match_88")
        new_mid = base_mid + 988000000
        new_match = SportMatch(mid=new_mid, sid=sid, lid=lid, aid=home_tid, bid=away_tid, mtime=match_time, parlay=False, gcount=gcount, running=False, mt=mt, state=state)
        session.add(new_match)
        await session.flush()
        self.logger.info(f"Successfully created match, mid: {new_mid}")
        return new_mid
    
    # 根据lid,aid,bid,mtime查询赛事
    async def get_match_by_lid_aid_bid_mtime(self, lid: int, aid: int, bid: int, mtime: datetime) -> Optional[int]:
        self.logger.info(f"Fetching match by lid: {lid}, aid: {aid}, bid: {bid}, mtime: {mtime}")
        async with self.async_session_factory() as session:
            stmt = select(SportMatch.mid).where(
                SportMatch.lid == lid,
                SportMatch.aid == aid,
                SportMatch.bid == bid,
                SportMatch.mtime == mtime
            )
            result = await session.execute(stmt)
            mid = result.scalar_one_or_none()

        return mid
    
    # 获取赛事信息
    async def get_match_info(self, mids: set[int]) -> list[dict]:
        async with self.async_session_factory() as session:
            result = await session.execute(
                select(SportMatch).where(SportMatch.mid.in_(mids))
            )
            rows = result.scalars().all()
            return [self.format_to_dict(row) for row in rows]
            

    # 更新match
    async def update_match_by_mid(self, mid: int, data: Dict[str, Any]):
        async with self.async_session_factory() as session:
            self.logger.info(f"Updating match with mid: {mid} with data: {data}")
            # Ensure the mid exists before updating
            stmt = update(SportMatch).where(SportMatch.mid == mid).values(**data)
            result = await session.execute(stmt)
            if result.rowcount == 0:
                self.logger.error(f"No match found with mid: {mid}")
                return

            await session.commit()
            # self.logger.info(f"Successfully updated match with mid: {mid}")

    async def batch_update_matches(self, update_data: List[Dict[str, Any]]):
        """
        批量更新赛事信息
        :param update_data: A list of dictionaries, where each dictionary contains the mid and the fields to update.
                            Example: [{'mid': 123, 'state': 1, 'gcount': 5}, {'mid': 456, 'state': 0}]
        """
        if not update_data:
            self.logger.info("No data provided for batch update.")
            return

        async with self.async_session_factory() as session:
            mids = [item['mid'] for item in update_data]
            
            update_fields = set()
            for item in update_data:
                update_fields.update(item.keys())
            update_fields.discard('mid')

            if not update_fields:
                self.logger.info("No fields to update in batch_update_matches.")
                return

            values = {}
            for field in update_fields:
                case_statement = case(
                    {item['mid']: item[field] for item in update_data if field in item},
                    value=SportMatch.mid,
                    else_=getattr(SportMatch, field)
                )
                values[field] = case_statement

            stmt = update(SportMatch).where(SportMatch.mid.in_(mids), SportMatch.sstate == 0).values(**values)
            
            await session.execute(stmt)
            await session.commit()
            # self.logger.info(f"Successfully batch updated {result.rowcount} matches.")

    # 设置赛事关盘
    async def set_match_closed(self, mid: set[int]):
        async with self.async_session_factory() as session:
            # self.logger.info(f"Setting match as closed with mid: {mid}")
            stmt = update(SportMatch).where(SportMatch.mid.in_(mid), SportMatch.copen == 1, SportMatch.state == 1).values(state=0, atime=func.now())
            result = await session.execute(stmt)
            if result.rowcount == 0:
                # self.logger.error(f"No match found with mid: {mid}")
                return

            await session.commit()
            # self.logger.info(f"Successfully set match as closed with mid: {mid}")

    async def upsert_sport_result_crawl(self, session: AsyncSession, result_data: Dict[str, Any]):
        # 生成 uk 字段
        result_data['uk'] = f"{result_data['mid']},{result_data['rid']},{result_data['hcp']}"

        # 构建 MERGE 语句
        merge_sql = text("""
            MERGE dbo.sport_result_crawl AS target
            USING (VALUES (:uk, :mid, :sid, :rid, :rcode, :hcp, :a, :b, :c, :type, :state))
                   AS source (uk, mid, sid, rid, rcode, hcp, a, b, c, type, state)
            ON (target.mid = source.mid AND target.rid = source.rid AND target.hcp = source.hcp)
            WHEN MATCHED THEN
                UPDATE SET
                    uk = source.uk,
                    sid = source.sid,
                    rcode = source.rcode,
                    hcp = source.hcp,
                    a = source.a,
                    b = source.b,
                    c = source.c,
                    type = source.type,
                    state = source.state,
                    updateAt = GETDATE()
            WHEN NOT MATCHED THEN
                INSERT (uk, mid, sid, rid, rcode, hcp, a, b, c, type, state, createAt, updateAt)
                VALUES (source.uk, source.mid, source.sid, source.rid, source.rcode, source.hcp, source.a, source.b, source.c, source.type, source.state, GETDATE(), GETDATE());
        """)

        # 执行 MERGE 语句
        await session.execute(merge_sql, result_data)
        await session.flush()
        self.logger.info(f"Upserted sport result: mid={result_data['mid']}, rid={result_data['rid']}, hcp={result_data['hcp']}")

    async def upsert_sport_process(self, process_data: Dict[str, Any]):
        async with self.async_session_factory() as session:            
            mid = process_data.get('mid')
            self.logger.info(f"Upserting sport process for mid: {mid}")

            # 尝试查询现有记录
            existing_process = await session.execute(
                select(SportProcess).where(SportProcess.mid == mid)
            )
            process_obj = existing_process.scalar_one_or_none()

            if process_obj:
                # 如果记录存在，则更新
                for key, value in process_data.items():
                    setattr(process_obj, key, value)
                self.logger.info(f"Updating existing sport process for mid: {mid}")
            else:
                # 如果记录不存在，则插入新记录
                process_obj = SportProcess(**process_data)
                session.add(process_obj)
                self.logger.info(f"Inserting new sport process for mid: {mid}")

            await session.commit()
            self.logger.info(f"Successfully upserted sport process for mid: {mid}")


class LogDatabaseManager(BaseDatabaseManager):
    def __init__(self, config: dict):
        super().__init__('log', config)

    async def add_logs(self, logs_data: List[Dict[str, Any]]):
        """Adds a batch of new records to the odd_logs table."""
        async with self.async_session_factory() as session:
            self.logger.info(f"Adding {len(logs_data)} logs in bulk.")
            log_objects = [OddLogs(**data) for data in logs_data]
            await session.execute(insert(OddLogs).values(log_objects))
            await session.commit()
            self.logger.info("Successfully added logs in bulk.")

    async def get_first_log(self,  match_id: int) -> Optional[OddLogs]:
        """Retrieves the first log entry from the odd_logs table for a given match_id, ordered by ID."""
        self.logger.info(f"Fetching the first log entry for match_id: {match_id}.")
        async with self.async_session_factory() as session:
            stmt = select(OddLogs).where(OddLogs.matchId == match_id).order_by(OddLogs.id).limit(1)
            result = await session.execute(stmt)
            log_entry = result.scalars().first()
        if log_entry:
            self.logger.info(f"Found first log entry with id: {log_entry.id} for match_id: {match_id}")
        else:
            self.logger.info(f"No log entries found for match_id: {match_id}.")
        return log_entry
