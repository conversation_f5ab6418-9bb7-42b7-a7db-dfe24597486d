{"code": 0, "msg": "成功！", "time": 1751684749, "data": {"given": 0, "rolling": 2, "finish": 4361, "list": [{"id": 4486, "tournament_name": "乔氏 33局抢17胜", "bisai_addtime": "1751679446", "zhandui_name_a": "刘海涛", "zhandui_name_b": "王云", "zhandui_a_logo": "https://v9.cool/api/uploads/20250625/1e11ccfcdded7a648607f883d9e7d01a.png", "zhandui_b_logo": "https://v9.cool/api/uploads/20250625/51d461460fd4466678b068bffddff09f.png", "bisai_endtime": "1751681400", "saishi_url": "", "a_jushu": "", "b_jushu": "", "a_max_fen": 150000, "b_max_fen": 150000, "max_fen": 150000, "addtime": 1751680369, "game_id": 7, "is_ok": 0, "is_settle": 0, "is_pan": 1, "a_max_peilv": 3.5, "b_max_peilv": 3.5, "danbi_max_fen": 10000, "danbi_min_fen": 50, "jingcai_text": [{"id": 1, "rule_name": "单挑让分", "aname": "刘海涛-2.5", "apeilv": "2.1", "aopen": "", "a_is_pan": 0, "bname": "王云+2.5", "bpeilv": "1.6", "bopen": "", "b_is_pan": 0, "cname": "", "cpeilv": "", "copen": "", "c_is_pan": 0, "kaijiang": ""}, {"id": 2, "rule_name": "单挑独赢", "aname": "刘海涛", "apeilv": "1.5", "aopen": "", "a_is_pan": 0, "bname": "王云", "bpeilv": "2.2", "bopen": "", "b_is_pan": 0, "cname": "", "cpeilv": "", "copen": "", "c_is_pan": 0, "kaijiang": ""}, {"id": 3, "rule_name": "", "aname": "刘海涛-1.5", "apeilv": "1.75", "aopen": "", "a_is_pan": 0, "bname": "王云+1.5", "bpeilv": "1.95", "bopen": "", "b_is_pan": 0, "cname": "", "cpeilv": "", "copen": "", "c_is_pan": 0, "kaijiang": ""}], "jingcai_text2": [], "jingcai_text3": [], "jingcai_text4": [], "jingcai_text5": [], "jingcai_text6": [], "call_url": null, "peilv_jine": 100, "del": 0, "single_user_limit": "10000.00"}, {"id": 4485, "tournament_name": "乔氏 33局抢17胜", "bisai_addtime": "1751679446", "zhandui_name_a": "陈思明", "zhandui_name_b": "薄俊杰", "zhandui_a_logo": "https://v9.cool/api/uploads/20250625/1e11ccfcdded7a648607f883d9e7d01a.png", "zhandui_b_logo": "https://v9.cool/api/uploads/20250625/51d461460fd4466678b068bffddff09f.png", "bisai_endtime": "1751681400", "saishi_url": "", "a_jushu": "", "b_jushu": "", "a_max_fen": 150000, "b_max_fen": 150000, "max_fen": 150000, "addtime": 1751680277, "game_id": 7, "is_ok": 0, "is_settle": 0, "is_pan": 1, "a_max_peilv": 3.5, "b_max_peilv": 3.5, "danbi_max_fen": 10000, "danbi_min_fen": 50, "jingcai_text": [{"id": 1, "rule_name": "单挑让分", "aname": "陈思明-2.5", "apeilv": "1.6", "aopen": "", "a_is_pan": 0, "bname": "薄俊杰+2.5", "bpeilv": "2.15", "bopen": "", "b_is_pan": 0, "cname": "", "cpeilv": "", "copen": "", "c_is_pan": 0, "kaijiang": ""}, {"id": 2, "rule_name": "单挑独赢", "aname": "陈思明", "apeilv": "1.1", "aopen": "", "a_is_pan": 0, "bname": "薄俊杰", "bpeilv": "2.6", "bopen": "", "b_is_pan": 0, "cname": "", "cpeilv": "", "copen": "", "c_is_pan": 0, "kaijiang": ""}, {"id": 3, "rule_name": "单挑让分", "aname": "陈思明-3.5", "apeilv": "1.9", "aopen": "", "a_is_pan": 0, "bname": "薄俊杰+3.5", "bpeilv": "1.8", "bopen": "", "b_is_pan": 0, "cname": "", "cpeilv": "", "copen": "", "c_is_pan": 0, "kaijiang": ""}], "jingcai_text2": [], "jingcai_text3": [], "jingcai_text4": [], "jingcai_text5": [], "jingcai_text6": [], "call_url": null, "peilv_jine": 100, "del": 0, "single_user_limit": "10000.00"}]}}