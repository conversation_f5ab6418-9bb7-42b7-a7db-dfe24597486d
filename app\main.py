import asyncio
import logging
import sys
import os
import signal

# Add project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.services.setup_logging import setup_logging
from app.config import (
    get_v9_config, get_telegram_config, get_timeout, 
    get_proxies, get_bet_items_id_mapping, get_result_id_mapping,
    get_mysql_config, get_odbc_config, get_redis_config
)

from app.db.mysql_db_manager import MysqlDBManager
from app.db.redis_manager import RedisManager
from app.db.mssql_db_manager import MatchDatabaseManager, LogDatabaseManager
from app.services.telegram_bot import  TelegramBot
from app.services.v9_crawler import V9Crawler
from app.services.data_process import DataProcessor
from app.services.go_sport import GoSportProcessor

def create_app():
    """
    Application factory.
    """
    # Load configs
    v9_config = get_v9_config()
    telegram_config = get_telegram_config()
    timeout = get_timeout()
    proxies = get_proxies()
    bet_items_id_mapping = get_bet_items_id_mapping()
    result_id_mapping = get_result_id_mapping()
    mysql_config = get_mysql_config()
    odbc_config = get_odbc_config()
    redis_config = get_redis_config()

    data_queue = asyncio.Queue(maxsize=100)
    result_queue = asyncio.Queue(maxsize=100) 

    # Initialize DB managers
    mysql_db = MysqlDBManager(mysql_config)
    match_db = MatchDatabaseManager(odbc_config)
    log_db = LogDatabaseManager(odbc_config)
    redis = RedisManager(redis_config)

    # Initialize services
    telegram_bot = TelegramBot(
        bot_token=telegram_config.get('bot_token'),
        chat_id=telegram_config.get('chat_id')
    )
    
    go_sport_processor = GoSportProcessor(
        bet_items_id_mapping, result_id_mapping, 
        match_db, redis, mysql_db, telegram_bot
    )

    data_processor = DataProcessor(mysql_db, data_queue, go_sport_processor)

    crawler = V9Crawler(
        mysql_db,
        data_queue,
        result_queue,
        telegram_bot,
        proxies=proxies,
        timeout=timeout,
        v9_urls=v9_config
    )

    return {
        "crawler": crawler,
        "telegram_bot": telegram_bot,
        "data_processor": data_processor,
        "go_sport_processor": go_sport_processor,
        "db_managers": {
            "mysql_db": mysql_db,
            "match_db": match_db,
            "log_db": log_db,
            "redis": redis
        }
    }

async def main():
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("程序启动中...")
    

    app = create_app()

    await app["telegram_bot"].send_message("程序启动中...")
    
    crawler = app["crawler"]
    data_processor = app["data_processor"]
    db_managers = app["db_managers"]
    redis = db_managers["redis"]

    await redis.connect()

    stop_event = asyncio.Event()

    def handle_sigterm():
        logger.info("收到 SIGTERM 信号，正在准备关闭...")
        stop_event.set()

    loop = asyncio.get_running_loop()
    try:
        loop.add_signal_handler(signal.SIGTERM, handle_sigterm)
    except NotImplementedError:
        logger.warning("无法添加 SIGTERM 信号处理器 (可能在 Windows 上运行)。")

    # Start all tasks in the background
    tasks = [
        asyncio.create_task(crawler.start_matches_crawling()),
        asyncio.create_task(crawler.start_results_crawling()),
        asyncio.create_task(data_processor.start_processing()),
        asyncio.create_task(data_processor.start_go_sport_processing())
    ]

    try:
        # Wait indefinitely until an interrupt signal is received
        await stop_event.wait()
    except (KeyboardInterrupt, asyncio.CancelledError):
        logger.info("程序被中断，开始清理...")
    finally:
        logger.info("正在尝试关闭所有子任务...")
        for task in tasks:
            if not task.done():
                task.cancel()
                try:
                    await task  # Wait for the task to acknowledge cancellation
                except asyncio.CancelledError:
                    pass  # Expected cancellation
        logger.info("所有子任务已关闭。")

        # Now, safely close shared resources
        await crawler.close()
        await db_managers["mysql_db"].close()
        await db_managers["match_db"].close()
        await db_managers["log_db"].close()
        await redis.close()
        
        logger.info("所有连接已关闭。")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"程序运行过程中发生错误: {e}")
    finally:
        import time
        time.sleep(0.1)
        print("程序即将退出...")
